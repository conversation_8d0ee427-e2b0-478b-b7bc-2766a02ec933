import { apiRequest, PaginatedResponse, BusinessIdeaProgress } from './api';

// ✅ UNIFIED: BusinessIdea interface matching backend BusinessIdeaSerializer exactly
export interface BusinessIdea {
  id: number;
  title: string;
  slug: string;
  description: string;
  problem_statement: string;
  solution_description: string;
  target_audience: string;
  market_opportunity: string | null;
  business_model: string | null;
  current_stage: 'concept' | 'validation' | 'development' | 'scaling' | 'established';
  image: string | null;
  // ✅ FIXED: Owner structure matches UserSerializer from backend
  owner: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    full_name?: string;
    is_active?: boolean;
    is_staff?: boolean;
    is_superuser?: boolean;
    date_joined?: string;
    last_login?: string;
    is_staff_member?: boolean;
    user_role?: 'admin' | 'moderator' | 'entrepreneur' | 'mentor' | 'investor' | 'user';
    role_assignments?: any[];
    profile?: {
      id?: number;
      bio?: string;
      location?: string;
      birth_date?: string;
      phone_number?: string;
      profile_image?: string;
      website?: string;
      linkedin_url?: string;
      company?: string;
      job_title?: string;
      industry?: string;
      experience_years?: number;
      expertise?: string;
      is_active?: boolean;
      completion_percentage?: number;
    };
  };
  // ✅ FIXED: Collaborators structure matches UserSerializer from backend
  collaborators: Array<{
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    full_name?: string;
    is_active?: boolean;
    user_role?: 'admin' | 'moderator' | 'entrepreneur' | 'mentor' | 'investor' | 'user';
    profile?: {
      id?: number;
      bio?: string;
      location?: string;
      profile_image?: string;
    };
  }>;
  tags: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
  moderation_status: 'approved' | 'pending' | 'rejected';
  moderation_comment: string | null;
  progress_count: number;
  created_at: string;
  updated_at: string;
  // ✅ FIXED: Write-only fields for API operations (matching backend serializer)
  owner_id?: number; // For creation
  collaborator_ids?: number[]; // For adding collaborators
  tag_ids?: number[]; // For adding tags
}

export interface ProgressUpdate {
  id: number;
  business_idea: number;
  business_idea_title: string;
  title: string;
  description: string;
  achievements: string;
  challenges: string | null;
  next_steps: string;
  created_by: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  created_at: string;
  updated_at: string;
}

export interface IncubatorResource {
  id: number;
  title: string;
  description: string;
  resource_type: 'article' | 'video' | 'template' | 'tool' | 'course' | 'ebook' | 'other';
  category: 'ideation' | 'validation' | 'planning' | 'finance' | 'marketing' | 'legal' | 'operations' | 'technology' | 'growth' | 'other';
  url: string;
  image: string | null;
  file: string | null;
  author: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  tags: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
  created_at: string;
  updated_at: string;
}

export interface MentorProfile {
  id: number;
  user: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    profile: {
      id: number;
      bio: string;
      location: string;
      expertise: string;
      profile_image: string | null;
    };
  };
  bio: string;
  company: string | null;
  position: string | null;
  years_of_experience: number;
  linkedin_profile: string | null;
  website: string | null;
  availability: 'high' | 'medium' | 'low' | 'limited';
  availability_display: string;
  max_mentees: number;
  is_accepting_mentees: boolean;
  is_verified: boolean;
  expertise_areas: MentorExpertise[];
  active_mentorships_count: number;
  created_at: string;
  updated_at: string;
}

export interface MentorExpertise {
  id: number;
  mentor: number;
  category: string;
  category_display: string;
  specific_expertise: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  level_display: string;
  years_experience: number;
}

export interface MentorshipApplication {
  id: number;
  business_idea: number;
  business_idea_title: string;
  applicant: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  preferred_mentor: MentorProfile | null;
  goals: string;
  specific_areas: string;
  commitment: string;
  preferred_communication: 'video' | 'phone' | 'email' | 'chat' | 'in_person';
  preferred_communication_display: string;
  preferred_expertise: string | null;
  preferred_expertise_display: string | null;
  status: 'pending' | 'approved' | 'rejected';
  admin_notes: string | null;
  created_at: string;
  updated_at: string;
}

export interface MentorshipMatch {
  id: number;
  mentor: MentorProfile;
  mentee: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  business_idea: number;
  business_idea_title: string;
  application: MentorshipApplication | null;
  status: 'active' | 'completed' | 'paused' | 'terminated';
  status_display: string;
  start_date: string;
  end_date: string | null;
  goals: string;
  focus_areas: string;
  mentee_notes: string | null;
  mentor_notes: string | null;
  sessions: MentorshipSession[];
  sessions_count: number;
  created_at: string;
  updated_at: string;
}

export interface MentorshipSession {
  id: number;
  mentorship_match: number;
  title: string;
  description: string | null;

  // Scheduling fields
  scheduled_at: string;
  duration_minutes: number;
  session_type: 'video' | 'phone' | 'in_person' | 'chat';
  session_type_display: string;
  location: string | null;

  // Video conferencing fields
  video_provider: 'jitsi' | 'google_meet' | 'microsoft_teams' | 'zoom' | 'custom' | null;
  video_provider_display: string | null;
  meeting_id: string | null;
  meeting_password: string | null;
  meeting_link: string | null;

  // Reminders
  reminder_sent: boolean;
  reminder_sent_at: string | null;

  // Calendar integration
  mentor_calendar_event_id: string | null;
  mentee_calendar_event_id: string | null;
  calendar_provider: string | null;

  // Status
  status: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled' | 'in_progress';
  status_display: string;

  // Notes
  mentor_notes: string | null;
  mentee_notes: string | null;

  // Feedback
  feedback: MentorshipFeedback[];
  average_rating: number | null;
  feedback_count: number;

  // Timestamps
  created_at: string;
  updated_at: string;
  started_at: string | null;
  ended_at: string | null;
}

export interface MentorshipFeedback {
  id: number;
  session: number;
  provided_by: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  is_from_mentee: boolean;

  // Overall rating
  rating: number;
  rating_display: string;

  // Detailed ratings
  knowledge_rating: number | null;
  knowledge_rating_display: string | null;
  communication_rating: number | null;
  communication_rating_display: string | null;
  helpfulness_rating: number | null;
  helpfulness_rating_display: string | null;
  preparation_rating: number | null;
  preparation_rating_display: string | null;
  responsiveness_rating: number | null;
  responsiveness_rating_display: string | null;

  // Feedback text
  comments: string;
  areas_of_improvement: string | null;
  highlights: string | null;

  // Session outcomes
  goals_achieved: boolean;
  follow_up_needed: boolean;
  follow_up_notes: string | null;

  // Privacy settings
  is_private: boolean;
  share_with_mentor: boolean;

  // Timestamps
  created_at: string;
  updated_at: string;
}

export interface InvestorProfile {
  id: number;
  user: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  investor_type: 'angel' | 'vc' | 'corporate' | 'government' | 'crowdfunding' | 'other';
  company_name: string | null;
  bio: string;
  investment_focus: string;
  investment_range_min: number;
  investment_range_max: number;
  linkedin_profile: string | null;
  website: string | null;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
}

export interface FundingOpportunity {
  id: number;
  title: string;
  description: string;
  funding_type: 'grant' | 'equity' | 'loan' | 'convertible' | 'prize' | 'crowdfunding' | 'other';
  amount: number;
  provider: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  eligibility_criteria: string;
  application_process: string;
  application_deadline: string;
  status: 'active' | 'closed' | 'draft';
  application_count: number;
  created_at: string;
  updated_at: string;
}

export interface FundingApplication {
  id: number;
  business_idea: number;
  business_idea_title: string;
  funding_opportunity: number;
  funding_opportunity_title: string;
  applicant: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  pitch: string;
  requested_amount: number;
  use_of_funds: string;
  status: 'pending' | 'shortlisted' | 'approved' | 'rejected' | 'funded';
  reviewer_notes: string | null;
  created_at: string;
  updated_at: string;
}

export interface Investment {
  id: number;
  business_idea: number;
  business_idea_title: string;
  investor: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  investment_type: 'equity' | 'loan' | 'convertible' | 'grant' | 'other';
  amount: number;
  equity_percentage: number | null;
  terms: string;
  status: 'proposed' | 'negotiating' | 'accepted' | 'completed' | 'declined';
  created_at: string;
  updated_at: string;
  completed_at: string | null;
}

// BusinessPlan interface removed - use the one from businessPlanApi.ts instead

export interface BusinessMilestone {
  id: number;
  business_idea: {
    id: number;
    title: string;
    user: {
      id: number;
      first_name: string;
      last_name: string;
    };
  };
  title: string;
  description?: string;
  milestone_type: 'revenue' | 'product' | 'team' | 'funding' | 'market' | 'other';
  status: 'not_started' | 'in_progress' | 'completed' | 'overdue' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  target_date?: string;
  completed_date?: string;
  target_value?: string;
  current_value?: string;
  progress_percentage?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

// Business Ideas API
export const businessIdeasAPI = {
  getBusinessIdeas: async () => {
    try {
      const response = await apiRequest<PaginatedResponse<BusinessIdea>>('/api/incubator/business-ideas/');

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for business ideas:', response);
      throw new Error('API returned unexpected format for business ideas');
    } catch (error) {
      console.error('Error fetching business ideas:', error);
      throw error;
    }
  },

  getBusinessIdea: (id: number) =>
    apiRequest<BusinessIdea>(`/api/incubator/business-ideas/${id}/`),

  getBusinessIdeaById: (id: number) =>
    apiRequest<BusinessIdea>(`/api/incubator/business-ideas/${id}/`),

  // ✅ FIXED: Create business idea with proper data structure matching backend serializer
  createBusinessIdea: (ideaData: Partial<BusinessIdea> & { owner_id: number }) => {
    // Ensure data structure matches backend expectations
    const requestData = {
      title: ideaData.title,
      description: ideaData.description,
      problem_statement: ideaData.problem_statement,
      solution_description: ideaData.solution_description,
      target_audience: ideaData.target_audience,
      market_opportunity: ideaData.market_opportunity || null,
      business_model: ideaData.business_model || null,
      current_stage: ideaData.current_stage || 'concept',
      owner_id: ideaData.owner_id,
      // Optional fields
      ...(ideaData.collaborator_ids && { collaborator_ids: ideaData.collaborator_ids }),
      ...(ideaData.tag_ids && { tag_ids: ideaData.tag_ids }),
    };
    return apiRequest<BusinessIdea>('/api/incubator/business-ideas/', 'POST', requestData);
  },

  updateBusinessIdea: (id: number, ideaData: Partial<BusinessIdea>) =>
    apiRequest<BusinessIdea>(`/api/incubator/business-ideas/${id}/`, 'PUT', ideaData),

  deleteBusinessIdea: (id: number) =>
    apiRequest<void>(`/api/incubator/business-ideas/${id}/`, 'DELETE'),

  addCollaborator: (ideaId: number, userId: number) =>
    apiRequest<{ message: string }>(`/incubator/business-ideas/${ideaId}/add_collaborator/`, 'POST', { user_id: userId }),

  removeCollaborator: (ideaId: number, userId: number) =>
    apiRequest<{ message: string }>(`/incubator/business-ideas/${ideaId}/remove_collaborator/`, 'POST', { user_id: userId }),

  moderateIdea: (ideaId: number, status: 'approved' | 'pending' | 'rejected', comment: string = '') =>
    apiRequest<{ message: string, idea: BusinessIdea }>(`/incubator/business-ideas/${ideaId}/moderate/`, 'POST', {
      moderation_status: status,
      moderation_comment: comment
    }),

  // Get progress tracking data for a business idea
  getProgressTracking: async (ideaId: number) => {
    try {
      return await apiRequest<BusinessIdeaProgress>(`/incubator/business-ideas/${ideaId}/progress_tracking/`);
    } catch (error) {
      console.error('Error fetching progress tracking data:', error);
      // Return default data on error
      return {
        total_updates: 0,
        updates_by_month: {},
        update_frequency: 0,
        recent_updates: [],
        common_achievement_themes: [],
        common_challenge_themes: [],
        current_stage: 'concept',
        idea_age_days: 0
      };
    }
  },
};

// Progress Updates API
export const progressUpdatesAPI = {
  getProgressUpdates: async (businessIdeaId?: number) => {
    try {
      const endpoint = businessIdeaId
        ? `/incubator/progress-updates/?business_idea=${businessIdeaId}`
        : '/api/incubator/progress-updates/';

      const response = await apiRequest<PaginatedResponse<ProgressUpdate>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for progress updates:', response);
      return [];
    } catch (error) {
      console.error('Error fetching progress updates:', error);
      return [];
    }
  },

  getProgressUpdate: (id: number) =>
    apiRequest<ProgressUpdate>(`/incubator/progress-updates/${id}/`),

  createProgressUpdate: (updateData: Partial<ProgressUpdate> & { business_idea: number, created_by_id: number }) =>
    apiRequest<ProgressUpdate>('/api/incubator/progress-updates/', 'POST', updateData),

  updateProgressUpdate: (id: number, updateData: Partial<ProgressUpdate>) =>
    apiRequest<ProgressUpdate>(`/incubator/progress-updates/${id}/`, 'PUT', updateData),

  deleteProgressUpdate: (id: number) =>
    apiRequest<void>(`/incubator/progress-updates/${id}/`, 'DELETE'),
};

// Incubator Resources API
export const incubatorResourcesAPI = {
  getIncubatorResources: async (category?: string) => {
    try {
      const endpoint = category
        ? `/api/incubator/resources/?category=${category}`
        : '/api/incubator/resources/';

      const response = await apiRequest<PaginatedResponse<IncubatorResource>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for incubator resources:', response);
      return [];
    } catch (error) {
      console.error('Error fetching incubator resources:', error);
      return [];
    }
  },

  getIncubatorResource: (id: number) =>
    apiRequest<IncubatorResource>(`/incubator/resources/${id}/`),

  createIncubatorResource: (resourceData: Partial<IncubatorResource> & { author_id: number }) =>
    apiRequest<IncubatorResource>('/api/incubator/resources/', 'POST', resourceData),

  updateIncubatorResource: (id: number, resourceData: Partial<IncubatorResource>) =>
    apiRequest<IncubatorResource>(`/incubator/resources/${id}/`, 'PUT', resourceData),

  deleteIncubatorResource: (id: number) =>
    apiRequest<void>(`/incubator/resources/${id}/`, 'DELETE'),
};

// Mentor Profiles API
export const mentorProfilesAPI = {
  getMentorProfiles: async () => {
    try {
      const response = await apiRequest<PaginatedResponse<MentorProfile>>('/api/incubator/mentor-profiles/');

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for mentor profiles:', response);
      return [];
    } catch (error) {
      console.error('Error fetching mentor profiles:', error);
      return [];
    }
  },

  getMentorProfile: (id: number) =>
    apiRequest<MentorProfile>(`/incubator/mentor-profiles/${id}/`),

  createMentorProfile: (profileData: Partial<MentorProfile> & { user_id: number }) =>
    apiRequest<MentorProfile>('/api/incubator/mentor-profiles/', 'POST', profileData),

  updateMentorProfile: (id: number, profileData: Partial<MentorProfile>) =>
    apiRequest<MentorProfile>(`/incubator/mentor-profiles/${id}/`, 'PUT', profileData),

  deleteMentorProfile: (id: number) =>
    apiRequest<void>(`/incubator/mentor-profiles/${id}/`, 'DELETE'),

  verifyMentor: (id: number) =>
    apiRequest<{ message: string, mentor_profile: MentorProfile }>(`/incubator/mentor-profiles/${id}/verify/`, 'POST'),

  unverifyMentor: (id: number) =>
    apiRequest<{ message: string, mentor_profile: MentorProfile }>(`/incubator/mentor-profiles/${id}/unverify/`, 'POST'),

  toggleAcceptingMentees: (id: number) =>
    apiRequest<{ message: string, mentor_profile: MentorProfile }>(`/incubator/mentor-profiles/${id}/toggle_accepting_mentees/`, 'POST'),
};

// Mentor Expertise API
export const mentorExpertiseAPI = {
  getMentorExpertise: async (mentorId?: number) => {
    try {
      const endpoint = mentorId
        ? `/incubator/mentor-expertise/?mentor_id=${mentorId}`
        : '/api/incubator/mentor-expertise/';

      const response = await apiRequest<PaginatedResponse<MentorExpertise>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for mentor expertise:', response);
      return [];
    } catch (error) {
      console.error('Error fetching mentor expertise:', error);
      return [];
    }
  },

  createMentorExpertise: (expertiseData: Partial<MentorExpertise> & { mentor: number }) =>
    apiRequest<MentorExpertise>('/api/incubator/mentor-expertise/', 'POST', expertiseData),

  updateMentorExpertise: (id: number, expertiseData: Partial<MentorExpertise>) =>
    apiRequest<MentorExpertise>(`/incubator/mentor-expertise/${id}/`, 'PUT', expertiseData),

  deleteMentorExpertise: (id: number) =>
    apiRequest<void>(`/incubator/mentor-expertise/${id}/`, 'DELETE'),
};

// Mentorship Applications API
export const mentorshipApplicationsAPI = {
  getMentorshipApplications: async (businessIdeaId?: number) => {
    try {
      const endpoint = businessIdeaId
        ? `/incubator/mentorship-applications/?business_idea=${businessIdeaId}`
        : '/api/incubator/mentorship-applications/';

      const response = await apiRequest<PaginatedResponse<MentorshipApplication>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for mentorship applications:', response);
      return [];
    } catch (error) {
      console.error('Error fetching mentorship applications:', error);
      return [];
    }
  },

  getMentorshipApplication: (id: number) =>
    apiRequest<MentorshipApplication>(`/incubator/mentorship-applications/${id}/`),

  createMentorshipApplication: (applicationData: Partial<MentorshipApplication> & { business_idea: number, applicant_id: number, preferred_mentor_id?: number }) =>
    apiRequest<MentorshipApplication>('/api/incubator/mentorship-applications/', 'POST', applicationData),

  updateMentorshipApplication: (id: number, applicationData: Partial<MentorshipApplication>) =>
    apiRequest<MentorshipApplication>(`/incubator/mentorship-applications/${id}/`, 'PUT', applicationData),

  deleteApplication: (id: number) =>
    apiRequest<void>(`/incubator/mentorship-applications/${id}/`, 'DELETE'),

  updateStatus: (id: number, status: 'pending' | 'approved' | 'rejected', adminNotes: string = '') =>
    apiRequest<{ message: string, application: MentorshipApplication }>(`/incubator/mentorship-applications/${id}/update_status/`, 'POST', {
      status,
      admin_notes: adminNotes
    }),
};

// Mentorship Matches API
export const mentorshipMatchesAPI = {
  getMentorshipMatches: async () => {
    try {
      const response = await apiRequest<PaginatedResponse<MentorshipMatch>>('/api/incubator/mentorship-matches/');

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for mentorship matches:', response);
      throw new Error('API returned unexpected format for mentorship matches');
    } catch (error) {
      console.error('Error fetching mentorship matches:', error);
      throw error;
    }
  },

  getMentorshipMatch: (id: number) =>
    apiRequest<MentorshipMatch>(`/incubator/mentorship-matches/${id}/`),

  createMentorshipMatch: (matchData: Partial<MentorshipMatch> & { mentor: number, business_idea: number, application_id?: number }) =>
    apiRequest<MentorshipMatch>('/api/incubator/mentorship-matches/', 'POST', matchData),

  updateMentorshipMatch: (id: number, matchData: Partial<MentorshipMatch>) =>
    apiRequest<MentorshipMatch>(`/incubator/mentorship-matches/${id}/`, 'PUT', matchData),

  deleteMentorshipMatch: (id: number) =>
    apiRequest<void>(`/incubator/mentorship-matches/${id}/`, 'DELETE'),

  updateStatus: (id: number, status: 'active' | 'completed' | 'paused' | 'terminated') =>
    apiRequest<{ message: string, match: MentorshipMatch }>(`/incubator/mentorship-matches/${id}/update_status/`, 'POST', {
      status
    }),
};

// Mentorship Sessions API
export const mentorshipSessionsAPI = {
  getMentorshipSessions: async (matchId?: number, status?: string) => {
    try {
      let endpoint = '/api/incubator/mentorship-sessions/';
      const params = [];

      if (matchId) {
        params.push(`match_id=${matchId}`);
      }

      if (status) {
        params.push(`status=${status}`);
      }

      if (params.length > 0) {
        endpoint += `?${params.join('&')}`;
      }

      const response = await apiRequest<PaginatedResponse<MentorshipSession>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for mentorship sessions:', response);
      return [];
    } catch (error) {
      console.error('Error fetching mentorship sessions:', error);
      return [];
    }
  },

  getUpcomingSessions: async () => {
    try {
      const response = await apiRequest<PaginatedResponse<MentorshipSession>>('/api/incubator/mentorship-sessions/upcoming/');

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for upcoming sessions:', response);
      return [];
    } catch (error) {
      console.error('Error fetching upcoming sessions:', error);
      return [];
    }
  },

  getPastSessions: async () => {
    try {
      const response = await apiRequest<PaginatedResponse<MentorshipSession>>('/api/incubator/mentorship-sessions/past/');

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for past sessions:', response);
      return [];
    } catch (error) {
      console.error('Error fetching past sessions:', error);
      return [];
    }
  },

  getMentorshipSession: (id: number) =>
    apiRequest<MentorshipSession>(`/incubator/mentorship-sessions/${id}/`),

  createMentorshipSession: (sessionData: Partial<MentorshipSession> & { mentorship_match: number }) =>
    apiRequest<MentorshipSession>('/api/incubator/mentorship-sessions/', 'POST', sessionData),

  updateMentorshipSession: (id: number, sessionData: Partial<MentorshipSession>) =>
    apiRequest<MentorshipSession>(`/incubator/mentorship-sessions/${id}/`, 'PUT', sessionData),

  deleteMentorshipSession: (id: number) =>
    apiRequest<void>(`/incubator/mentorship-sessions/${id}/`, 'DELETE'),

  // Session status management
  startSession: (id: number) =>
    apiRequest<{ message: string, session: MentorshipSession }>(`/incubator/mentorship-sessions/${id}/start_session/`, 'POST'),

  completeSession: (id: number) =>
    apiRequest<{ message: string, session: MentorshipSession }>(`/incubator/mentorship-sessions/${id}/complete_session/`, 'POST'),

  cancelSession: (id: number) =>
    apiRequest<{ message: string, session: MentorshipSession }>(`/incubator/mentorship-sessions/${id}/cancel_session/`, 'POST'),

  rescheduleSession: (id: number, scheduledAt: string) =>
    apiRequest<{ message: string, session: MentorshipSession }>(`/incubator/mentorship-sessions/${id}/reschedule_session/`, 'POST', {
      scheduled_at: scheduledAt
    }),

  // Video conferencing
  createMeeting: (id: number, videoProvider: string, customMeetingLink?: string) =>
    apiRequest<{ message: string, meeting_details: any }>(`/incubator/mentorship-sessions/${id}/create_meeting/`, 'POST', {
      video_provider: videoProvider,
      meeting_link: customMeetingLink
    }),

  // Calendar integration
  addToCalendar: (id: number, data: { calendar_provider: string }) =>
    apiRequest<{ message: string, event_details: any }>(`/incubator/mentorship-sessions/${id}/add_to_calendar/`, 'POST', data),

  updateCalendar: (id: number) =>
    apiRequest<{ message: string, event_details: any }>(`/incubator/mentorship-sessions/${id}/update_calendar/`, 'POST'),

  removeFromCalendar: (id: number) =>
    apiRequest<{ message: string }>(`/incubator/mentorship-sessions/${id}/remove_from_calendar/`, 'POST'),

  getAvailability: (startDate: string, endDate: string, calendarProvider: string) =>
    apiRequest<{ available_slots: Array<{ start: string, end: string }> }>(
      `/incubator/mentorship-sessions/get_availability/?start_date=${startDate}&end_date=${endDate}&calendar_provider=${calendarProvider}`
    ),
};

// Mentorship Feedback API
export const mentorshipFeedbackAPI = {
  getMentorshipFeedback: async (sessionId?: number) => {
    try {
      const endpoint = sessionId
        ? `/incubator/mentorship-feedback/?session=${sessionId}`
        : '/api/incubator/mentorship-feedback/';

      const response = await apiRequest<PaginatedResponse<MentorshipFeedback>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for mentorship feedback:', response);
      throw new Error('API returned unexpected format for mentorship feedback');
    } catch (error) {
      console.error('Error fetching mentorship feedback:', error);
      throw error;
    }
  },

  getSessionFeedback: async (sessionId: number) => {
    try {
      const response = await apiRequest<PaginatedResponse<MentorshipFeedback>>(`/incubator/mentorship-feedback/session_feedback/?session_id=${sessionId}`);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for session feedback:', response);
      return [];
    } catch (error) {
      console.error('Error fetching session feedback:', error);
      return [];
    }
  },

  getMentorRatings: async (mentorId: number) => {
    try {
      const response = await apiRequest<{
        mentor: any;
        feedback_count: number;
        overall_rating: number;
        detailed_ratings: {
          knowledge: number;
          communication: number;
          helpfulness: number;
          preparation: number;
          responsiveness: number;
        };
        rating_counts: Record<string, number>;
        recent_feedback: MentorshipFeedback[];
      }>(`/incubator/mentorship-feedback/mentor_ratings/?mentor_id=${mentorId}`);

      return response;
    } catch (error) {
      console.error('Error fetching mentor ratings:', error);
      throw error;
    }
  },

  createMentorshipFeedback: (feedbackData: Partial<MentorshipFeedback> & { session: number, is_from_mentee: boolean }) =>
    apiRequest<MentorshipFeedback>('/api/incubator/mentorship-feedback/', 'POST', feedbackData),

  updateMentorshipFeedback: (id: number, feedbackData: Partial<MentorshipFeedback>) =>
    apiRequest<MentorshipFeedback>(`/incubator/mentorship-feedback/${id}/`, 'PUT', feedbackData),

  deleteMentorshipFeedback: (id: number) =>
    apiRequest<void>(`/incubator/mentorship-feedback/${id}/`, 'DELETE'),

  togglePrivacy: (id: number) =>
    apiRequest<MentorshipFeedback>(`/incubator/mentorship-feedback/${id}/toggle_privacy/`, 'POST'),
};

// Investor Profiles API
export const investorProfilesAPI = {
  getInvestorProfiles: async () => {
    try {
      const response = await apiRequest<PaginatedResponse<InvestorProfile>>('/api/incubator/investor-profiles/');

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for investor profiles:', response);
      throw new Error('API returned unexpected format for investor profiles');
    } catch (error) {
      console.error('Error fetching investor profiles:', error);
      throw error;
    }
  },

  getInvestorProfile: (id: number) =>
    apiRequest<InvestorProfile>(`/incubator/investor-profiles/${id}/`),

  createInvestorProfile: (profileData: Partial<InvestorProfile> & { user_id: number }) =>
    apiRequest<InvestorProfile>('/api/incubator/investor-profiles/', 'POST', profileData),

  updateInvestorProfile: (id: number, profileData: Partial<InvestorProfile>) =>
    apiRequest<InvestorProfile>(`/incubator/investor-profiles/${id}/`, 'PUT', profileData),

  deleteInvestorProfile: (id: number) =>
    apiRequest<void>(`/incubator/investor-profiles/${id}/`, 'DELETE'),

  verifyInvestor: (id: number) =>
    apiRequest<{ message: string, investor_profile: InvestorProfile }>(`/incubator/investor-profiles/${id}/verify/`, 'POST'),

  unverifyInvestor: (id: number) =>
    apiRequest<{ message: string, investor_profile: InvestorProfile }>(`/incubator/investor-profiles/${id}/unverify/`, 'POST'),
};

// Funding Opportunities API
export const fundingOpportunitiesAPI = {
  getFundingOpportunities: async () => {
    try {
      const response = await apiRequest<PaginatedResponse<FundingOpportunity>>('/api/incubator/funding-opportunities/');

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for funding opportunities:', response);
      throw new Error('API returned unexpected format for funding opportunities');
    } catch (error) {
      console.error('Error fetching funding opportunities:', error);
      throw error;
    }
  },

  getFundingOpportunity: (id: number) =>
    apiRequest<FundingOpportunity>(`/incubator/funding-opportunities/${id}/`),

  createFundingOpportunity: (opportunityData: Partial<FundingOpportunity> & { provider_id: number }) =>
    apiRequest<FundingOpportunity>('/api/incubator/funding-opportunities/', 'POST', opportunityData),

  updateFundingOpportunity: (id: number, opportunityData: Partial<FundingOpportunity>) =>
    apiRequest<FundingOpportunity>(`/incubator/funding-opportunities/${id}/`, 'PUT', opportunityData),

  deleteFundingOpportunity: (id: number) =>
    apiRequest<void>(`/incubator/funding-opportunities/${id}/`, 'DELETE'),

  activateFundingOpportunity: (id: number) =>
    apiRequest<{ message: string, opportunity: FundingOpportunity }>(`/incubator/funding-opportunities/${id}/activate/`, 'POST'),

  closeFundingOpportunity: (id: number) =>
    apiRequest<{ message: string, opportunity: FundingOpportunity }>(`/incubator/funding-opportunities/${id}/close/`, 'POST'),
};

// Funding Applications API
export const fundingApplicationsAPI = {
  getFundingApplications: async (businessIdeaId?: number, fundingOpportunityId?: number) => {
    try {
      let endpoint = '/api/incubator/funding-applications/';
      const params = [];

      if (businessIdeaId) {
        params.push(`business_idea=${businessIdeaId}`);
      }

      if (fundingOpportunityId) {
        params.push(`funding_opportunity=${fundingOpportunityId}`);
      }

      if (params.length > 0) {
        endpoint += `?${params.join('&')}`;
      }

      const response = await apiRequest<PaginatedResponse<FundingApplication>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for funding applications:', response);
      return [];
    } catch (error) {
      console.error('Error fetching funding applications:', error);
      return [];
    }
  },

  getFundingApplication: (id: number) =>
    apiRequest<FundingApplication>(`/incubator/funding-applications/${id}/`),

  createFundingApplication: (applicationData: Partial<FundingApplication> & { business_idea: number, funding_opportunity: number, applicant_id: number }) =>
    apiRequest<FundingApplication>('/api/incubator/funding-applications/', 'POST', applicationData),

  updateFundingApplication: (id: number, applicationData: Partial<FundingApplication>) =>
    apiRequest<FundingApplication>(`/incubator/funding-applications/${id}/`, 'PUT', applicationData),

  deleteFundingApplication: (id: number) =>
    apiRequest<void>(`/incubator/funding-applications/${id}/`, 'DELETE'),

  updateStatus: (id: number, status: 'pending' | 'shortlisted' | 'approved' | 'rejected' | 'funded', reviewerNotes: string = '') =>
    apiRequest<{ message: string, application: FundingApplication }>(`/incubator/funding-applications/${id}/update_status/`, 'POST', {
      status,
      reviewer_notes: reviewerNotes
    }),
};

// Investments API
export const investmentsAPI = {
  getInvestments: async (businessIdeaId?: number) => {
    try {
      const endpoint = businessIdeaId
        ? `/incubator/investments/?business_idea=${businessIdeaId}`
        : '/api/incubator/investments/';

      const response = await apiRequest<PaginatedResponse<Investment>>(endpoint);

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for investments:', response);
      return [];
    } catch (error) {
      console.error('Error fetching investments:', error);
      return [];
    }
  },

  getInvestment: (id: number) =>
    apiRequest<Investment>(`/incubator/investments/${id}/`),

  createInvestment: (investmentData: Partial<Investment> & { business_idea: number, investor_id: number }) =>
    apiRequest<Investment>('/api/incubator/investments/', 'POST', investmentData),

  updateInvestment: (id: number, investmentData: Partial<Investment>) =>
    apiRequest<Investment>(`/incubator/investments/${id}/`, 'PUT', investmentData),

  deleteInvestment: (id: number) =>
    apiRequest<void>(`/incubator/investments/${id}/`, 'DELETE'),

  updateStatus: (id: number, status: 'proposed' | 'negotiating' | 'accepted' | 'completed' | 'declined') =>
    apiRequest<{ message: string, investment: Investment }>(`/incubator/investments/${id}/update_status/`, 'POST', {
      status
    }),
};

// Business Plans API - REMOVED: Use businessPlansAPI from businessPlanApi.ts instead
// This duplicate API has been consolidated to avoid confusion

// Business Milestones API
export const businessMilestonesAPI = {
  getBusinessMilestones: async () => {
    try {
      const response = await apiRequest<PaginatedResponse<BusinessMilestone>>('/api/incubator/business-milestones/');

      if (response && typeof response === 'object' && 'results' in response && Array.isArray(response.results)) {
        return response.results;
      } else if (Array.isArray(response)) {
        return response;
      }

      console.error('API returned unexpected format for business milestones:', response);
      return [];
    } catch (error) {
      console.error('Error fetching business milestones:', error);
      return [];
    }
  },

  getBusinessMilestone: (id: number) =>
    apiRequest<BusinessMilestone>(`/incubator/business-milestones/${id}/`),

  createBusinessMilestone: (milestoneData: Partial<BusinessMilestone> & { business_idea: number }) =>
    apiRequest<BusinessMilestone>('/api/incubator/business-milestones/', 'POST', milestoneData),

  updateBusinessMilestone: (id: number, milestoneData: Partial<BusinessMilestone>) =>
    apiRequest<BusinessMilestone>(`/incubator/business-milestones/${id}/`, 'PUT', milestoneData),

  deleteBusinessMilestone: (id: number) =>
    apiRequest<void>(`/incubator/business-milestones/${id}/`, 'DELETE'),

  updateStatus: (id: number, status: 'not_started' | 'in_progress' | 'completed' | 'overdue' | 'cancelled') =>
    apiRequest<{ message: string, milestone: BusinessMilestone }>(`/incubator/business-milestones/${id}/update_status/`, 'POST', {
      status
    }),
};
