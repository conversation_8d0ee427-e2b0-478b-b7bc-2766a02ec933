/**
 * 🎯 CONSOLIDATED APP ROUTES
 * Simplified, maintainable routing system with unified role-based access control
 * 
 * CONSOLIDATION BENEFITS:
 * - Single routing component instead of multiple complex routers
 * - Unified role validation logic
 * - Cleaner route definitions
 * - Easier to maintain and extend
 * - Eliminates duplicate routing logic
 */

import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useUnifiedRoles } from '../hooks/useUnifiedRoles'; // ✅ FIXED: Use only unified roles

// ✅ FIXED: Use only ConsolidatedProtectedRoute for consistency
import ConsolidatedProtectedRoute from './ConsolidatedProtectedRoute';

// Layout components
import Layout from '../components/layout/Layout';
import UserLayout from '../components/layout/UserLayout';

// Public pages
import HomePage from '../pages/HomePage';
import { FeaturesPage } from '../pages/FeaturesPage';
import PublicEventsPage from '../pages/PublicEventsPage';
import PublicResourcesPage from '../pages/PublicResourcesPage';
import LoginPage from '../pages/LoginPage';
import EnhancedRegisterPage from '../pages/EnhancedRegisterPage';
import RegistrationSuccessPage from '../pages/RegistrationSuccessPage';
import LogoutPage from '../pages/LogoutPage';
import AccessDeniedPage from '../pages/AccessDeniedPage';
import TermsPage from '../pages/TermsPage';
import PrivacyPage from '../pages/PrivacyPage';
import ABTestDashboard from '../components/ABTestAdmin/ABTestDashboard';

// Protected pages
import AIChatPage from '../pages/ai/AIChatPage';
import UserProfilePage from '../pages/UserProfilePage';
import SettingsPage from '../pages/SettingsPage';
import CommunityPage from '../pages/CommunityPage';




// ✅ UNIFIED: Use ConsolidatedPageRouter directly instead of wrapper components
import { ConsolidatedPageRouter } from './ConsolidatedPageRouter';

// Dedicated routers - REMOVED: SuperAdminRouter deleted (old code)

// ✅ REMOVED: Duplicate dynamic routers - using ConsolidatedPageRouter for all routing

// ========================================
// LOADING COMPONENT
// ========================================

const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
  </div>
);

// ========================================
// DASHBOARD REDIRECT COMPONENT
// ========================================

const DashboardRedirect = () => {
  const { primaryRole, getHomeRoute } = useUnifiedRoles(); // ✅ UNIFIED: Single role service

  console.log('🔄 DashboardRedirect - Primary Role:', primaryRole);

  // Use unified role system for consistent redirects
  const homeRoute = getHomeRoute();
  console.log('✅ Redirecting to unified home route:', homeRoute);

  return <Navigate to={homeRoute} replace />;
};

// ========================================
// ROLE-BASED PAGE COMPONENT
// ========================================

interface RoleBasedPageProps {
  children: React.ReactNode;
  allowedRoles?: string[];
}

const RoleBasedPage: React.FC<RoleBasedPageProps> = ({
  children,
  allowedRoles = ['entrepreneur', 'mentor', 'investor', 'moderator', 'admin'] // REMOVED 'user' and 'super_admin' (single role system)
}) => {
  return (
    <ConsolidatedProtectedRoute allowedRoles={allowedRoles}>
      {children}
    </ConsolidatedProtectedRoute>
  );
};

// ========================================
// MAIN APP ROUTES COMPONENT
// ========================================

const ConsolidatedAppRoutes: React.FC = () => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <Routes>
        {/* ========================================
            PUBLIC ROUTES
        ======================================== */}
        
        <Route path="/" element={<HomePage />} />
        <Route path="/features" element={<FeaturesPage />} />
        <Route path="/events" element={<PublicEventsPage />} />
        <Route path="/resources" element={<PublicResourcesPage />} />
        <Route path="/community" element={<CommunityPage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<EnhancedRegisterPage />} />
        <Route path="/terms" element={<TermsPage />} />
        <Route path="/privacy" element={<PrivacyPage />} />



        {/* Consolidated registration success routes */}
        <Route path="/registration-success" element={<RegistrationSuccessPage />} />
        <Route path="/register/success" element={<RegistrationSuccessPage />} />
        <Route path="/signup/success" element={<RegistrationSuccessPage />} />

        <Route path="/logout" element={<LogoutPage />} />
        <Route path="/access-denied" element={<AccessDeniedPage />} />

        {/* Public AI Chat access */}
        <Route path="/ai-chat" element={<AIChatPage />} />

        {/* Community page - accessible to all users (public and authenticated) */}
        <Route path="/community" element={<CommunityPage />} />

        {/* User Profile by ID - accessible to authenticated users for viewing specific user profiles */}
        <Route path="/profile/:id" element={
          <ConsolidatedProtectedRoute allowedRoles={['entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'user']}>
            <UserProfilePage />
          </ConsolidatedProtectedRoute>
        } />

        {/* Business Incubator - accessible to all authenticated users */}
        <Route path="/incubator" element={
          <ConsolidatedProtectedRoute allowedRoles={['entrepreneur', 'mentor', 'investor', 'admin']}>
            <ConsolidatedPageRouter pageType="incubator" />
          </ConsolidatedProtectedRoute>
        } />

        {/* ========================================
            REGULAR USER ROUTES (SIMPLE LAYOUT)
        ======================================== */}

        <Route path="/user" element={<UserLayout />}>
          {/* Home routes for regular users */}
          <Route path="home" element={
            <ConsolidatedProtectedRoute allowedRoles={['user']}>
              <ConsolidatedPageRouter pageType="home" />
            </ConsolidatedProtectedRoute>
          } />

          {/* Basic profile for regular users */}
          <Route path="profile" element={
            <ConsolidatedProtectedRoute allowedRoles={['user']}>
              <UserProfilePage />
            </ConsolidatedProtectedRoute>
          } />

          {/* Basic settings for regular users */}
          <Route path="settings" element={
            <ConsolidatedProtectedRoute allowedRoles={['user']}>
              <SettingsPage />
            </ConsolidatedProtectedRoute>
          } />
        </Route>

        {/* Dashboard redirect for authenticated users - MOVED OUTSIDE LAYOUT */}
        <Route path="/dashboard" element={<DashboardRedirect />} />

        {/* ========================================
            ADMIN REDIRECT (NO SIDEBAR)
        ======================================== */}

        {/* Admin Root Route - Redirect to dashboard */}
        <Route path="/admin" element={
          <Navigate to="/admin/dashboard" replace />
        } />







        {/* Legacy Super Admin Route - redirects to admin */}
        <Route path="/super_admin/*" element={
          <Navigate to="/admin/dashboard" replace />
        } />

        {/* ========================================
            PROTECTED ROUTES WITH SIDEBAR LAYOUT
        ======================================== */}

        <Route path="/" element={<Layout />}>

          {/* ========================================
              ADMIN ROUTES (WITH SIDEBAR)
          ======================================== */}

          {/* Admin Dashboard Route - Main admin dashboard */}
          <Route path="/admin/dashboard" element={
            <ConsolidatedProtectedRoute allowedRoles={['admin']}>
              <ConsolidatedPageRouter pageType="admin-dashboard" />
            </ConsolidatedProtectedRoute>
          } />

          {/* ✅ UNIFIED: Dashboard routes - All roles including admin */}
          <Route path="/:role/dashboard" element={
            <ConsolidatedProtectedRoute allowedRoles={['entrepreneur', 'mentor', 'investor', 'moderator', 'admin']}>
              <ConsolidatedPageRouter pageType="dashboard" />
            </ConsolidatedProtectedRoute>
          } />

          {/* Profile routes - EXCLUDED regular users */}
          <Route path="/:role/profile" element={
            <ConsolidatedProtectedRoute allowedRoles={['entrepreneur', 'mentor', 'investor', 'moderator', 'admin']}>
              <UserProfilePage />
            </ConsolidatedProtectedRoute>
          } />

          {/* Settings routes - EXCLUDED regular users */}
          <Route path="/:role/settings" element={
            <ConsolidatedProtectedRoute allowedRoles={['entrepreneur', 'mentor', 'investor', 'moderator', 'admin']}>
              <SettingsPage />
            </ConsolidatedProtectedRoute>
          } />

          {/* AI Chat routes - EXCLUDED regular users */}
          <Route path="/:role/ai-chat" element={
            <ConsolidatedProtectedRoute allowedRoles={['entrepreneur', 'mentor', 'investor', 'moderator', 'admin']}>
              <AIChatPage />
            </ConsolidatedProtectedRoute>
          } />

          {/* Business Incubator routes - Available to entrepreneurs, mentors, investors, and admins */}
          <Route path="/:role/incubator" element={
            <ConsolidatedProtectedRoute allowedRoles={['entrepreneur', 'mentor', 'investor', 'admin']}>
              <ConsolidatedPageRouter pageType="incubator" />
            </ConsolidatedProtectedRoute>
          } />

          {/* Business Plans routes - Available to business roles */}
          <Route path="/:role/business-plans" element={
            <ConsolidatedProtectedRoute allowedRoles={['entrepreneur', 'mentor', 'investor', 'admin']}>
              <ConsolidatedPageRouter pageType="business-plans" />
            </ConsolidatedProtectedRoute>
          } />

          {/* Business Plans Detail routes - Available to business roles */}
          <Route path="/:role/business-plans/:id" element={
            <ConsolidatedProtectedRoute allowedRoles={['entrepreneur', 'mentor', 'investor', 'admin']}>
              <ConsolidatedPageRouter pageType="business-plans-detail" />
            </ConsolidatedProtectedRoute>
          } />

          {/* Business Ideas routes - Available to business roles */}
          <Route path="/:role/business-ideas" element={
            <ConsolidatedProtectedRoute allowedRoles={['entrepreneur', 'mentor', 'investor', 'admin']}>
              <ConsolidatedPageRouter pageType="business-ideas" />
            </ConsolidatedProtectedRoute>
          } />

          {/* Business Ideas Detail routes - Available to business roles */}
          <Route path="/:role/business-ideas/:id" element={
            <ConsolidatedProtectedRoute allowedRoles={['entrepreneur', 'mentor', 'investor', 'admin']}>
              <ConsolidatedPageRouter pageType="business-ideas-detail" />
            </ConsolidatedProtectedRoute>
          } />

          {/* AI Chat routes - Available to business roles */}
          <Route path="/:role/ai-chat" element={
            <ConsolidatedProtectedRoute allowedRoles={['entrepreneur', 'mentor', 'investor', 'admin', 'moderator']}>
              <ConsolidatedPageRouter pageType="ai-chat" />
            </ConsolidatedProtectedRoute>
          } />

          {/* User Settings routes - Available to business roles */}
          <Route path="/:role/settings" element={
            <ConsolidatedProtectedRoute allowedRoles={['entrepreneur', 'mentor', 'investor', 'moderator']}>
              <ConsolidatedPageRouter pageType="user-settings" />
            </ConsolidatedProtectedRoute>
          } />
          
          {/* ========================================
              ADMIN MANAGEMENT ROUTES (WITH SIDEBAR)
          ======================================== */}

          {/* Admin User Management */}
          <Route path="/admin/users" element={
            <ConsolidatedProtectedRoute allowedRoles={['admin']}>
              <ConsolidatedPageRouter pageType="users" />
            </ConsolidatedProtectedRoute>
          } />

          <Route path="/admin/user-management" element={
            <ConsolidatedProtectedRoute allowedRoles={['admin']}>
              <ConsolidatedPageRouter pageType="user-management" />
            </ConsolidatedProtectedRoute>
          } />

          {/* Admin System Management */}
          <Route path="/admin/system" element={
            <ConsolidatedProtectedRoute allowedRoles={['admin']}>
              <ConsolidatedPageRouter pageType="system" />
            </ConsolidatedProtectedRoute>
          } />

          <Route path="/admin/system-health" element={
            <ConsolidatedProtectedRoute allowedRoles={['admin']}>
              <ConsolidatedPageRouter pageType="health" />
            </ConsolidatedProtectedRoute>
          } />

          {/* Admin Analytics & Reports */}
          <Route path="/admin/analytics" element={
            <ConsolidatedProtectedRoute allowedRoles={['admin']}>
              <ConsolidatedPageRouter pageType="analytics" />
            </ConsolidatedProtectedRoute>
          } />

          <Route path="/admin/reports" element={
            <ConsolidatedProtectedRoute allowedRoles={['admin']}>
              <ConsolidatedPageRouter pageType="audit" />
            </ConsolidatedProtectedRoute>
          } />

          {/* Admin AI Management */}
          <Route path="/admin/ai-monitoring" element={
            <ConsolidatedProtectedRoute allowedRoles={['admin']}>
              <ConsolidatedPageRouter pageType="ai-monitoring" />
            </ConsolidatedProtectedRoute>
          } />

          <Route path="/admin/ai-analytics" element={
            <ConsolidatedProtectedRoute allowedRoles={['admin']}>
              <ConsolidatedPageRouter pageType="ai-analytics" />
            </ConsolidatedProtectedRoute>
          } />

          {/* Admin Security & Integrations */}
          <Route path="/admin/security" element={
            <ConsolidatedProtectedRoute allowedRoles={['admin']}>
              <ConsolidatedPageRouter pageType="security" />
            </ConsolidatedProtectedRoute>
          } />

          <Route path="/admin/integrations" element={
            <ConsolidatedProtectedRoute allowedRoles={['admin']}>
              <ConsolidatedPageRouter pageType="integrations" />
            </ConsolidatedProtectedRoute>
          } />

          {/* Admin Content Management */}
          <Route path="/admin/content" element={
            <ConsolidatedProtectedRoute allowedRoles={['admin']}>
              <ConsolidatedPageRouter pageType="content" />
            </ConsolidatedProtectedRoute>
          } />



        </Route>

        {/* ========================================
            FALLBACK ROUTES
        ======================================== */}
        
        {/* Catch-all route for 404 */}
        <Route path="*" element={
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-800 mb-4">404</h1>
              <p className="text-gray-600 mb-4">Page not found</p>
              <Navigate to="/" replace />
            </div>
          </div>
        } />
        
      </Routes>
    </Suspense>
  );
};

export default ConsolidatedAppRoutes;
