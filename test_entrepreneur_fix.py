#!/usr/bin/env python3
"""
Test Script for Entrepreneur Business Ideas Fix
Verifies that all the files and components are properly configured
"""

import os
import sys
import json
import re

class EntrepreneurFixTester:
    def __init__(self):
        self.test_results = []
        self.base_path = os.path.dirname(os.path.abspath(__file__))
        self.frontend_path = os.path.join(self.base_path, 'frontend', 'src')
        
    def log_test(self, test_name, success, details=""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")
        self.test_results.append({
            'test': test_name,
            'success': success,
            'details': details
        })
    
    def test_file_exists(self, file_path, description):
        """Test if a file exists"""
        full_path = os.path.join(self.frontend_path, file_path)
        exists = os.path.exists(full_path)
        self.log_test(f"File exists: {description}", exists, f"Path: {file_path}")
        return exists
    
    def test_file_contains(self, file_path, pattern, description):
        """Test if a file contains a specific pattern"""
        full_path = os.path.join(self.frontend_path, file_path)
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
                contains = pattern in content
                self.log_test(f"File contains: {description}", contains, f"Pattern: {pattern}")
                return contains
        except Exception as e:
            self.log_test(f"File contains: {description}", False, f"Error: {e}")
            return False
    
    def test_import_statement(self, file_path, import_statement, description):
        """Test if a file contains a specific import statement"""
        return self.test_file_contains(file_path, import_statement, description)
    
    def run_all_tests(self):
        """Run all tests"""
        print("🧪 Testing Entrepreneur Business Ideas Fix")
        print("=" * 60)
        
        # Test 1: Check if new entrepreneur business idea detail page exists
        self.test_file_exists(
            "pages/entrepreneur/EntrepreneurBusinessIdeaDetailPage.tsx",
            "Entrepreneur Business Idea Detail Page"
        )
        
        # Test 2: Check if entrepreneur business ideas page exists
        self.test_file_exists(
            "pages/entrepreneur/EntrepreneurBusinessIdeasPage.tsx",
            "Entrepreneur Business Ideas Page"
        )
        
        # Test 3: Check component registry imports
        self.test_import_statement(
            "config/componentRegistry.ts",
            "import EntrepreneurBusinessIdeaDetailPage from '../pages/entrepreneur/EntrepreneurBusinessIdeaDetailPage'",
            "Component registry imports detail page"
        )
        
        # Test 4: Check component registry entries
        self.test_file_contains(
            "config/componentRegistry.ts",
            "'business-ideas-detail-entrepreneur'",
            "Component registry has entrepreneur detail entry"
        )
        
        # Test 5: Check routes configuration
        self.test_file_contains(
            "routes/ConsolidatedAppRoutes.tsx",
            "/:role/business-ideas/:id",
            "Routes include business idea detail route"
        )
        
        # Test 6: Check API method exists
        self.test_file_contains(
            "services/incubatorApi.ts",
            "getBusinessIdeaById",
            "API has getBusinessIdeaById method"
        )
        
        # Test 7: Check API endpoints are consistent
        self.test_file_contains(
            "services/incubatorApi.ts",
            "/api/incubator/business-ideas/",
            "API endpoints use consistent path"
        )
        
        # Test 8: Check BusinessIdeaForm props are correct
        self.test_file_contains(
            "pages/entrepreneur/EntrepreneurBusinessIdeasPage.tsx",
            "isSubmitting={businessIdeasCRUD.isLoading}",
            "BusinessIdeaForm uses correct props"
        )
        
        # Test 9: Check mode prop is included
        self.test_file_contains(
            "pages/entrepreneur/EntrepreneurBusinessIdeasPage.tsx",
            'mode="create"',
            "BusinessIdeaForm has mode prop for create"
        )
        
        # Test 10: Check component selection logic
        self.test_file_contains(
            "config/componentRegistry.ts",
            "business-ideas-detail",
            "Component registry handles detail page selection"
        )
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   - {result['test']}: {result['details']}")
        
        return failed_tests == 0

def main():
    """Main test function"""
    tester = EntrepreneurFixTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed! The entrepreneur business ideas fix is properly implemented.")
        print("\n📋 What was fixed:")
        print("   ✅ Created EntrepreneurBusinessIdeaDetailPage.tsx")
        print("   ✅ Added missing routes for business idea details")
        print("   ✅ Updated component registry for entrepreneur-specific pages")
        print("   ✅ Fixed BusinessIdeaForm component props")
        print("   ✅ Fixed API endpoint consistency")
        print("   ✅ Added proper component selection logic")
        
        print("\n🚀 Ready to test in browser:")
        print("   1. Start backend: cd backend && python manage.py runserver")
        print("   2. Start frontend: cd frontend && npm start")
        print("   3. Navigate to /entrepreneur/business-ideas")
        print("   4. Create a business idea and click on it to view details")
        
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        sys.exit(1)

if __name__ == "__main__":
    main()
