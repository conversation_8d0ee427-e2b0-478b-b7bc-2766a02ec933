#!/usr/bin/env python
"""
Test script to verify that the API returns the correct role for entrepreneur1 user
This simulates what the frontend would receive when making API calls
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from users.serializers.main import UserSerializer
from core.services.unified_role_service import UnifiedRoleService

def test_entrepreneur_role_assignment():
    """Test that entrepreneur1 user gets the correct role assignment"""
    
    print("🧪 Testing entrepreneur role assignment...")
    print("=" * 50)
    
    try:
        # Get the entrepreneur1 user
        user = User.objects.get(username='entrepreneur1')
        print(f"✅ Found user: {user.username} (ID: {user.id})")
        
        # Test 1: UserSerializer (what the /api/users/users/me/ endpoint returns)
        serializer = UserSerializer(user)
        user_data = serializer.data
        user_role = user_data.get('user_role')
        
        print(f"\n📡 API Response Test:")
        print(f"   UserSerializer.user_role: '{user_role}'")
        
        # Test 2: UnifiedRoleService (what the role service returns)
        unified_service = UnifiedRoleService()
        primary_role = unified_service.get_user_primary_role(user)
        
        print(f"   UnifiedRoleService.get_user_primary_role: '{primary_role}'")
        
        # Test 3: Expected routing behavior
        expected_dashboard_route = f"/{user_role}/dashboard" if user_role != 'user' else '/user/home'
        
        print(f"\n🛣️  Routing Test:")
        print(f"   Expected dashboard route: '{expected_dashboard_route}'")
        
        # Test 4: Verify this matches frontend expectations
        valid_business_roles = ['entrepreneur', 'mentor', 'investor', 'moderator', 'admin']
        is_business_role = user_role in valid_business_roles
        
        print(f"\n✅ Validation:")
        print(f"   Is business role: {is_business_role}")
        print(f"   Should access /entrepreneur/dashboard: {user_role == 'entrepreneur'}")
        print(f"   Should NOT access /user/home: {user_role != 'user'}")
        
        # Final result
        if user_role == 'entrepreneur':
            print(f"\n🎉 SUCCESS! entrepreneur1 user correctly gets 'entrepreneur' role")
            print(f"   ✅ Frontend should redirect to: /entrepreneur/dashboard")
            print(f"   ✅ User should have dashboard access")
            print(f"   ✅ Role-based routing should work correctly")
            return True
        else:
            print(f"\n❌ FAILURE! entrepreneur1 user gets '{user_role}' instead of 'entrepreneur'")
            return False
            
    except User.DoesNotExist:
        print("❌ ERROR: User 'entrepreneur1' not found")
        return False
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_entrepreneur_role_assignment()
    sys.exit(0 if success else 1)
