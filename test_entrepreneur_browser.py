#!/usr/bin/env python3
"""
Browser-based End-to-End Test for Entrepreneur Pages
Tests the frontend pages and functionality
"""

import time
import sys
from playwright.sync_api import sync_playwright

class EntrepreneurBrowserTester:
    def __init__(self):
        self.browser = None
        self.page = None
        self.test_results = []
        
    def log_test(self, test_name, success, details=""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")
        self.test_results.append({
            'test': test_name,
            'success': success,
            'details': details
        })
    
    def setup_browser(self):
        """Setup browser and page"""
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.launch(headless=False)  # Show browser for demo
            self.page = self.browser.new_page()
            self.page.set_viewport_size({"width": 1280, "height": 720})
            return True
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            return False
    
    def test_frontend_server(self):
        """Test if frontend server is accessible"""
        try:
            # Try different possible frontend URLs
            urls = [
                "http://localhost:3000",
                "http://localhost:5173",  # Vite default
                "http://localhost:4173",  # Vite preview
                "http://127.0.0.1:3000",
                "http://127.0.0.1:5173"
            ]
            
            for url in urls:
                try:
                    response = self.page.goto(url, timeout=5000)
                    if response and response.status < 400:
                        self.log_test("Frontend Server", True, f"Accessible at {url}")
                        return url
                except:
                    continue
            
            self.log_test("Frontend Server", False, "No frontend server found")
            return None
            
        except Exception as e:
            self.log_test("Frontend Server", False, f"Error: {e}")
            return None
    
    def test_backend_integration(self):
        """Test backend integration by accessing API directly"""
        try:
            # Navigate to backend admin to verify it's running
            response = self.page.goto("http://localhost:8000/admin/", timeout=10000)
            success = response and response.status == 200
            
            if success:
                # Check if we can see Django admin
                title = self.page.title()
                has_admin = "Django" in title or "admin" in title.lower()
                self.log_test("Backend Integration", has_admin, 
                             f"Django admin accessible, title: {title}")
            else:
                self.log_test("Backend Integration", False, 
                             f"Backend not accessible, status: {response.status if response else 'No response'}")
            
            return success
            
        except Exception as e:
            self.log_test("Backend Integration", False, f"Error: {e}")
            return False
    
    def test_api_endpoints(self):
        """Test API endpoints directly"""
        try:
            # Test API root
            response = self.page.goto("http://localhost:8000/api/", timeout=10000)
            api_success = response and response.status == 200
            
            if api_success:
                content = self.page.content()
                has_api_content = "api" in content.lower() or "rest" in content.lower()
                self.log_test("API Endpoints", has_api_content, "API root accessible")
            else:
                self.log_test("API Endpoints", False, "API root not accessible")
            
            return api_success
            
        except Exception as e:
            self.log_test("API Endpoints", False, f"Error: {e}")
            return False
    
    def test_entrepreneur_routes(self, base_url):
        """Test entrepreneur-specific routes"""
        try:
            # Test entrepreneur routes that should exist
            routes = [
                "/entrepreneur/dashboard",
                "/entrepreneur/business-plans", 
                "/entrepreneur/business-ideas",
                "/incubator"
            ]
            
            success_count = 0
            for route in routes:
                try:
                    url = f"{base_url}{route}"
                    response = self.page.goto(url, timeout=10000)
                    
                    if response and response.status < 400:
                        # Check if page loaded properly
                        title = self.page.title()
                        has_content = len(self.page.content()) > 1000  # Basic content check
                        
                        if has_content:
                            success_count += 1
                            self.log_test(f"Route {route}", True, f"Loaded, title: {title}")
                        else:
                            self.log_test(f"Route {route}", False, "Page loaded but minimal content")
                    else:
                        self.log_test(f"Route {route}", False, 
                                     f"Status: {response.status if response else 'No response'}")
                        
                except Exception as e:
                    self.log_test(f"Route {route}", False, f"Error: {e}")
            
            return success_count > 0
            
        except Exception as e:
            self.log_test("Entrepreneur Routes", False, f"Error: {e}")
            return False
    
    def test_layout_consistency(self, base_url):
        """Test that layouts are consistent (no duplicate sidebars)"""
        try:
            # Navigate to entrepreneur dashboard
            url = f"{base_url}/entrepreneur/dashboard"
            response = self.page.goto(url, timeout=10000)
            
            if response and response.status < 400:
                # Wait for page to load
                self.page.wait_for_timeout(2000)
                
                # Check for duplicate sidebars
                sidebars = self.page.query_selector_all('[class*="sidebar"], [class*="nav"], nav')
                sidebar_count = len(sidebars)
                
                # Check for duplicate layouts
                layouts = self.page.query_selector_all('[class*="layout"], [class*="container"]')
                layout_count = len(layouts)
                
                # Basic check - should not have excessive sidebars
                no_duplicate_sidebars = sidebar_count <= 2  # Allow for mobile + desktop
                
                self.log_test("Layout Consistency", no_duplicate_sidebars, 
                             f"Sidebars: {sidebar_count}, Layouts: {layout_count}")
                return no_duplicate_sidebars
            else:
                self.log_test("Layout Consistency", False, "Could not access dashboard")
                return False
                
        except Exception as e:
            self.log_test("Layout Consistency", False, f"Error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup browser resources"""
        try:
            if self.page:
                self.page.close()
            if self.browser:
                self.browser.close()
            if hasattr(self, 'playwright'):
                self.playwright.stop()
        except:
            pass
    
    def run_all_tests(self):
        """Run all browser tests"""
        print("🌐 Starting Browser-based Entrepreneur Tests")
        print("=" * 60)
        
        # Setup browser
        if not self.setup_browser():
            return False
        
        try:
            # Test backend first
            self.test_backend_integration()
            self.test_api_endpoints()
            
            # Test frontend
            frontend_url = self.test_frontend_server()
            
            if frontend_url:
                # Test entrepreneur-specific functionality
                self.test_entrepreneur_routes(frontend_url)
                self.test_layout_consistency(frontend_url)
            else:
                print("⚠️  Frontend server not running - skipping frontend tests")
                print("   To test frontend, run: cd frontend && npm run dev")
            
            # Print summary
            print("\n" + "=" * 60)
            print("📊 BROWSER TEST SUMMARY")
            print("=" * 60)
            
            total_tests = len(self.test_results)
            passed_tests = sum(1 for result in self.test_results if result['success'])
            failed_tests = total_tests - passed_tests
            
            print(f"Total Tests: {total_tests}")
            print(f"✅ Passed: {passed_tests}")
            print(f"❌ Failed: {failed_tests}")
            print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
            
            if failed_tests > 0:
                print("\n❌ FAILED TESTS:")
                for result in self.test_results:
                    if not result['success']:
                        print(f"   - {result['test']}: {result['details']}")
            
            return failed_tests == 0
            
        finally:
            self.cleanup()

if __name__ == "__main__":
    tester = EntrepreneurBrowserTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
