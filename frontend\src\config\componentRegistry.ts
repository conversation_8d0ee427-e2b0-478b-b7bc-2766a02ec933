/**
 * 🎯 DYNAMIC COMPONENT REGISTRY
 * Reduces hardcoding in route management and makes it easier to add new pages
 */

import React from 'react';

// ========================================
// COMPONENT IMPORTS
// ========================================

// Dashboard pages (consolidated - using unified DashboardPage)
import DashboardPage from '../pages/DashboardPage';

// Admin pages
import AdminDashboardPage from '../pages/admin/AdminDashboardPage';
import UserManagementPage from '../pages/admin/UserManagementPage';
import SystemManagementPage from '../pages/admin/SystemManagementPage';
import SystemHealthPage from '../pages/admin/SystemHealthPage';
import AuditLogsPage from '../pages/admin/AuditLogsPage';
import AISystemPage from '../pages/admin/AISystemPage';
import SystemLogsPage from '../pages/admin/SystemLogsPage';

// Analytics pages - using admin analytics instead
import AnalyticsPage from '../pages/admin/AnalyticsPage';

// Profile pages
import UserProfilePage from '../pages/UserProfilePage';

// Settings pages
import SettingsPage from '../pages/SettingsPage';

// Home pages - using main HomePage
import HomePage from '../pages/HomePage';

// Incubator pages
import IncubatorPage from '../pages/IncubatorPage';

// Business-specific pages
import BusinessPlansListPage from '../pages/dashboard/BusinessPlansListPage';
import BusinessIdeasPage from '../pages/dashboard/BusinessIdeasPage';
import BusinessIdeaDetailPage from '../pages/dashboard/BusinessIdeaDetailPage';

// Entrepreneur-specific pages
import EntrepreneurDashboardPage from '../pages/entrepreneur/EntrepreneurDashboardPage';
import EntrepreneurBusinessIdeasPage from '../pages/entrepreneur/EntrepreneurBusinessIdeasPage';
import EntrepreneurBusinessIdeaDetailPage from '../pages/entrepreneur/EntrepreneurBusinessIdeaDetailPage';

// AI and other pages
import AIChatPage from '../pages/ai/AIChatPage';

// ========================================
// COMPONENT REGISTRY INTERFACE
// ========================================

export interface ComponentRegistryEntry {
  component: React.ComponentType<any>;
  roles: string[];
  lazy?: boolean; // For future lazy loading
}

// ========================================
// CENTRALIZED COMPONENT REGISTRY
// ========================================

export const COMPONENT_REGISTRY: Record<string, ComponentRegistryEntry> = {
  // Dashboard - Generic for most roles
  'dashboard': {
    component: DashboardPage,
    roles: ['admin', 'mentor', 'investor', 'moderator']
  },

  // Entrepreneur-specific dashboard
  'dashboard-entrepreneur': {
    component: EntrepreneurDashboardPage,
    roles: ['entrepreneur']
  },

  // Home
  'home': {
    component: HomePage,
    roles: ['user']
  },

  // User Management - Admin only (single role system)
  'users': {
    component: UserManagementPage,
    roles: ['admin']
  },
  'user-management': {
    component: UserManagementPage,
    roles: ['admin']
  },

  // User Approvals - Admin only (single role system)
  'approvals': {
    component: UserManagementPage,
    roles: ['admin']
  },

  // Admin Dashboard (main admin page)
  'admin-dashboard': {
    component: AdminDashboardPage,
    roles: ['admin']
  },

  // System Management - Admin only
  'system': {
    component: SystemManagementPage,
    roles: ['admin']
  },

  // System Health - Admin only
  'health': {
    component: SystemHealthPage,
    roles: ['admin']
  },
  'system-health': {
    component: SystemHealthPage,
    roles: ['admin']
  },

  // Audit Logs - Admin only
  'audit': {
    component: AuditLogsPage,
    roles: ['admin']
  },
  'logs': {
    component: SystemLogsPage,
    roles: ['admin']
  },

  // AI System Monitoring - Admin only
  'ai-monitoring': {
    component: AISystemPage,
    roles: ['admin']
  },

  // AI Analytics Dashboard - Admin only
  'ai-analytics': {
    component: React.lazy(() => import('../pages/admin/AIAnalyticsPage')),
    roles: ['admin']
  },

  // Security (using SystemManagementPage for now)
  'security': {
    component: SystemManagementPage,
    roles: ['admin']
  },

  // Integrations (using SystemManagementPage for now)
  'integrations': {
    component: SystemManagementPage,
    roles: ['admin']
  },

  // Content Management - REMOVED: ContentManagementPage deleted (had mock data)
  // 'content-admin': Use AdminDashboardPage instead

  // ✅ CLEANED UP: Removed registry entries for deleted super admin pages - functionality integrated into main dashboard
  
  // Analytics
  'analytics': {
    component: AnalyticsPage,
    roles: ['admin', 'entrepreneur', 'mentor', 'investor', 'moderator']  // ✅ CONSOLIDATED: Removed super_admin
  },

  // ✅ CLEANED UP: Removed reports entry - functionality integrated into main dashboard
  
  // Profile
  'profile': {
    component: UserProfilePage,
    roles: ['user', 'admin', 'entrepreneur', 'mentor', 'investor', 'moderator']  // ✅ CONSOLIDATED: Removed super_admin
  },

  // Settings
  'settings': {
    component: SettingsPage,  // ✅ FIXED: Use proper SettingsPage component
    roles: ['admin', 'entrepreneur', 'mentor', 'investor', 'moderator']
  },

  // Business Incubator
  'incubator': {
    component: IncubatorPage,
    roles: ['entrepreneur', 'mentor', 'investor', 'admin']
  },

  // Business Plans - Available to business roles
  'business-plans': {
    component: BusinessPlansListPage,
    roles: ['entrepreneur', 'mentor', 'investor', 'admin']
  },

  // Business Ideas - Available to business roles
  'business-ideas': {
    component: BusinessIdeasPage,
    roles: ['mentor', 'investor', 'admin']
  },

  // Entrepreneur-specific business ideas
  'business-ideas-entrepreneur': {
    component: EntrepreneurBusinessIdeasPage,
    roles: ['entrepreneur']
  },

  // Business Ideas Detail - Available to business roles
  'business-ideas-detail': {
    component: BusinessIdeaDetailPage,
    roles: ['mentor', 'investor', 'admin']
  },

  // Entrepreneur-specific business ideas detail
  'business-ideas-detail-entrepreneur': {
    component: EntrepreneurBusinessIdeaDetailPage,
    roles: ['entrepreneur']
  },

  // AI Chat - Available to business roles
  'ai-chat': {
    component: AIChatPage,
    roles: ['entrepreneur', 'mentor', 'investor', 'admin', 'moderator']
  },

  // User Settings - Available to business roles
  'user-settings': {
    component: SettingsPage,
    roles: ['entrepreneur', 'mentor', 'investor', 'moderator']
  }
};

// ========================================
// UTILITY FUNCTIONS
// ========================================

export function getComponentForPage(pageId: string, role: string): React.ComponentType<any> | null {
  // Special handling for dashboard - entrepreneurs get their own dashboard
  if (pageId === 'dashboard' && role === 'entrepreneur') {
    const entrepreneurDashboard = COMPONENT_REGISTRY['dashboard-entrepreneur'];
    if (entrepreneurDashboard && entrepreneurDashboard.roles.includes(role)) {
      return entrepreneurDashboard.component;
    }
  }

  // Special handling for business-ideas - entrepreneurs get their own page
  if (pageId === 'business-ideas' && role === 'entrepreneur') {
    const entrepreneurBusinessIdeas = COMPONENT_REGISTRY['business-ideas-entrepreneur'];
    if (entrepreneurBusinessIdeas && entrepreneurBusinessIdeas.roles.includes(role)) {
      return entrepreneurBusinessIdeas.component;
    }
  }

  // Special handling for business-ideas-detail - entrepreneurs get their own page
  if (pageId === 'business-ideas-detail' && role === 'entrepreneur') {
    const entrepreneurBusinessIdeasDetail = COMPONENT_REGISTRY['business-ideas-detail-entrepreneur'];
    if (entrepreneurBusinessIdeasDetail && entrepreneurBusinessIdeasDetail.roles.includes(role)) {
      return entrepreneurBusinessIdeasDetail.component;
    }
  }

  // Try role-specific component first
  const roleSpecificKey = `${pageId}-${role}`;
  if (COMPONENT_REGISTRY[roleSpecificKey]) {
    const entry = COMPONENT_REGISTRY[roleSpecificKey];
    if (entry.roles.includes(role)) {
      return entry.component;
    }
  }

  // Fall back to generic component
  const genericEntry = COMPONENT_REGISTRY[pageId];
  if (genericEntry && genericEntry.roles.includes(role)) {
    return genericEntry.component;
  }

  return null;
}

export function getAllRegisteredPages(): string[] {
  return Object.keys(COMPONENT_REGISTRY)
    .filter(key => !key.includes('-')) // Exclude role-specific variants
    .sort();
}

export function getComponentsForRole(role: string): Record<string, React.ComponentType<any>> {
  const components: Record<string, React.ComponentType<any>> = {};
  
  Object.entries(COMPONENT_REGISTRY).forEach(([key, entry]) => {
    if (entry.roles.includes(role)) {
      // Remove role suffix for cleaner key
      const cleanKey = key.replace(`-${role}`, '');
      components[cleanKey] = entry.component;
    }
  });
  
  return components;
}

// ========================================
// VALIDATION FUNCTIONS
// ========================================

export function validateComponentRegistry(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  Object.entries(COMPONENT_REGISTRY).forEach(([key, entry]) => {
    if (!entry.component) {
      errors.push(`Missing component for key: ${key}`);
    }
    
    if (!entry.roles || entry.roles.length === 0) {
      errors.push(`Missing roles for key: ${key}`);
    }
  });
  
  return {
    valid: errors.length === 0,
    errors
  };
}
