"""
API Views - Clean Version
Contains only essential ViewSets and business logic.
All admin functionality moved to users.views.admin_dashboard
"""

from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import models
from datetime import timedelta
from django.contrib.auth.models import User
from django_filters.rest_framework import DjangoFilterBackend
from django.core.cache import cache
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator
from core.services.unified_role_service import unified_role_service
from .models import Event, Resource, Post, Comment, Tag, MembershipApplication
from .serializers import EventSerializer, ResourceSerializer, PostSerializer, CommentSerializer, TagSerializer, MembershipApplicationSerializer
from users.permissions import IsAdminUser
import logging

logger = logging.getLogger(__name__)

# ========================================
# CORE BUSINESS VIEWSETS
# ========================================

class EventViewSet(viewsets.ModelViewSet):
    """Event management ViewSet"""
    queryset = Event.objects.all().order_by('-date')
    serializer_class = EventSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['location', 'is_virtual', 'moderation_status']
    search_fields = ['title', 'description', 'location']
    ordering_fields = ['date', 'created_at', 'title']

    def get_queryset(self):
        if self.request.user.is_authenticated and not unified_role_service.has_role(self.request.user, 'admin'):
            return Event.objects.filter(
                models.Q(moderation_status='approved') |
                models.Q(organizer=self.request.user)
            ).order_by('-date')
        return super().get_queryset()

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.AllowAny]
        elif self.action in ['destroy', 'update', 'partial_update']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def perform_update(self, serializer):
        event = self.get_object()
        if not (unified_role_service.has_role(self.request.user, 'admin') or
                event.organizer == self.request.user):
            raise permissions.PermissionDenied("You can only edit your own events")
        serializer.save()

    def perform_destroy(self, instance):
        if not (unified_role_service.has_role(self.request.user, 'admin') or
                instance.organizer == self.request.user):
            raise permissions.PermissionDenied("You can only delete your own events")
        instance.delete()

    def perform_create(self, serializer):
        serializer.save(organizer_id=self.request.user.id)


class ResourceViewSet(viewsets.ModelViewSet):
    """Resource management ViewSet"""
    queryset = Resource.objects.all().order_by('-created_at')
    serializer_class = ResourceSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['resource_type', 'moderation_status']
    search_fields = ['title', 'description', 'resource_type']
    ordering_fields = ['created_at', 'title']

    def get_queryset(self):
        if self.request.user.is_authenticated and not unified_role_service.has_role(self.request.user, 'admin'):
            return Resource.objects.filter(
                models.Q(moderation_status='approved') |
                models.Q(author=self.request.user)
            ).order_by('-created_at')
        return super().get_queryset()

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.AllowAny]
        elif self.action in ['destroy', 'update', 'partial_update']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def perform_update(self, serializer):
        resource = self.get_object()
        if not (unified_role_service.has_role(self.request.user, 'admin') or
                resource.author == self.request.user):
            raise permissions.PermissionDenied("You can only edit your own resources")
        serializer.save()

    def perform_destroy(self, instance):
        if not (unified_role_service.has_role(self.request.user, 'admin') or
                instance.author == self.request.user):
            raise permissions.PermissionDenied("You can only delete your own resources")
        instance.delete()

    def perform_create(self, serializer):
        serializer.save(author_id=self.request.user.id)


class PostViewSet(viewsets.ModelViewSet):
    """Post management ViewSet"""
    queryset = Post.objects.all().order_by('-created_at')
    serializer_class = PostSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['moderation_status']
    search_fields = ['title', 'content']
    ordering_fields = ['created_at', 'title']

    def get_queryset(self):
        if self.request.user.is_authenticated and not unified_role_service.has_role(self.request.user, 'admin'):
            return Post.objects.filter(
                models.Q(moderation_status='approved') |
                models.Q(author=self.request.user)
            ).order_by('-created_at')
        return super().get_queryset()

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.AllowAny]
        elif self.action in ['destroy', 'update', 'partial_update']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def perform_update(self, serializer):
        post = self.get_object()
        if not (unified_role_service.has_role(self.request.user, 'admin') or
                post.author == self.request.user):
            raise permissions.PermissionDenied("You can only edit your own posts")
        serializer.save()

    def perform_destroy(self, instance):
        if not (unified_role_service.has_role(self.request.user, 'admin') or
                instance.author == self.request.user):
            raise permissions.PermissionDenied("You can only delete your own posts")
        instance.delete()

    def perform_create(self, serializer):
        serializer.save(author_id=self.request.user.id)


class CommentViewSet(viewsets.ModelViewSet):
    """Comment management ViewSet"""
    queryset = Comment.objects.all().order_by('-created_at')
    serializer_class = CommentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['post']
    search_fields = ['content']
    ordering_fields = ['created_at']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.AllowAny]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    def perform_create(self, serializer):
        serializer.save(author_id=self.request.user.id)


class TagViewSet(viewsets.ModelViewSet):
    """Tag management ViewSet"""
    queryset = Tag.objects.all()
    serializer_class = TagSerializer
    filter_backends = [filters.SearchFilter]
    search_fields = ['name']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.AllowAny]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]


class MembershipApplicationViewSet(viewsets.ModelViewSet):
    """Membership application ViewSet"""
    queryset = MembershipApplication.objects.all().order_by('-created_at')
    serializer_class = MembershipApplicationSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status']
    search_fields = ['full_name', 'email']
    ordering_fields = ['created_at']

    def get_permissions(self):
        if self.action in ['create']:
            permission_classes = [permissions.AllowAny]
        else:
            permission_classes = [IsAdminUser]
        return [permission() for permission in permission_classes]


# ========================================
# UTILITY ENDPOINTS
# ========================================

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def analytics_overview(request):
    """Analytics overview endpoint"""
    try:
        time_range = request.GET.get('time_range', '30d')
        
        if time_range == '1d':
            days = 1
        elif time_range == '7d':
            days = 7
        elif time_range == '30d':
            days = 30
        else:
            days = 30

        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)

        total_users = User.objects.count()
        active_users = User.objects.filter(last_login__gte=start_date).count()
        new_users = User.objects.filter(date_joined__gte=start_date).count()

        return Response({
            'total_users': total_users,
            'active_users': active_users,
            'new_users': new_users,
            'time_range': time_range,
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat()
        })

    except Exception as e:
        logger.error(f"Error in analytics_overview: {e}")
        return Response(
            {'error': 'Failed to fetch analytics data'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def log_frontend_error(request):
    """Log frontend errors for monitoring and debugging"""
    try:
        error_data = request.data
        error_type = error_data.get('type', 'unknown')
        message = error_data.get('message', 'No message provided')
        severity = error_data.get('severity', 'medium')
        component = error_data.get('component', 'unknown')
        user_id = request.user.id if request.user.is_authenticated else None

        logger_instance = logging.getLogger('frontend_errors')
        log_message = f"Frontend Error - Type: {error_type}, Component: {component}, Message: {message}"

        if severity == 'critical':
            logger_instance.critical(log_message, extra={'user_id': user_id, 'error_data': error_data})
        elif severity == 'high':
            logger_instance.error(log_message, extra={'user_id': user_id, 'error_data': error_data})
        elif severity == 'medium':
            logger_instance.warning(log_message, extra={'user_id': user_id, 'error_data': error_data})
        else:
            logger_instance.info(log_message, extra={'user_id': user_id, 'error_data': error_data})

        return Response({'message': 'Error logged successfully'})

    except Exception as e:
        logger.error(f"Error logging frontend error: {e}")
        return Response(
            {'error': 'Failed to log error'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# ========================================
# ADMIN FUNCTIONALITY NOTICE
# ========================================
# All admin functions have been moved to users.views.admin_dashboard
# to provide a clean, consolidated admin system without conflicts.
#
# Available Admin Endpoints:
# - /api/users/admin/dashboard/stats/     - Dashboard statistics
# - /api/users/admin/system/health/       - System health monitoring  
# - /api/users/admin/activity/logs/       - Activity logs
# - /api/users/approvals/                 - User approval management
# - /api/users/role-applications/         - Role application management
#
# User Approval Workflow:
# 1. Users register via /api/auth/register/
# 2. UserApproval created automatically (if needed)
# 3. Admin approves via /api/users/approvals/{id}/approve/
# 4. Users can login after approval
# ========================================
