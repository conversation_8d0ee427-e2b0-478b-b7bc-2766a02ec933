import { test, expect } from '@playwright/test';

test.describe('Homepage Tests', () => {
  test('should load the homepage successfully', async ({ page }) => {
    await page.goto('/');
    
    // Check if the page loads without errors
    await expect(page).toHaveTitle(/Ya<PERSON>een AI/);
    
    // Check for main navigation elements
    await expect(page.locator('nav')).toBeVisible();
    
    // Check for main content area
    await expect(page.locator('main, #root')).toBeVisible();
  });

  test('should display the main hero section', async ({ page }) => {
    await page.goto('/');
    
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');
    
    // Check for hero section or main heading
    const heroSection = page.locator('h1, [data-testid="hero"], .hero');
    await expect(heroSection.first()).toBeVisible();
  });

  test('should have working navigation links', async ({ page }) => {
    await page.goto('/');
    
    // Wait for navigation to be visible
    await page.waitForSelector('nav', { timeout: 10000 });
    
    // Check if navigation contains expected links
    const nav = page.locator('nav');
    await expect(nav).toBeVisible();
    
    // Look for common navigation items
    const navItems = ['Home', 'About', 'Features', 'Login', 'Register'];
    
    for (const item of navItems) {
      const link = page.locator(`nav a:has-text("${item}"), nav button:has-text("${item}")`);
      if (await link.count() > 0) {
        await expect(link.first()).toBeVisible();
      }
    }
  });

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    // Check if the page is still functional on mobile
    await expect(page.locator('#root')).toBeVisible();
    
    // Check if navigation adapts to mobile (hamburger menu, etc.)
    const mobileNav = page.locator('[data-testid="mobile-menu"], .mobile-menu, button[aria-label*="menu"]');
    if (await mobileNav.count() > 0) {
      await expect(mobileNav.first()).toBeVisible();
    }
  });

  test('should handle language switching if available', async ({ page }) => {
    await page.goto('/');
    
    // Look for language switcher
    const langSwitcher = page.locator('[data-testid="language-switcher"], .language-switcher, button:has-text("العربية"), button:has-text("English")');
    
    if (await langSwitcher.count() > 0) {
      await expect(langSwitcher.first()).toBeVisible();
      
      // Try clicking the language switcher
      await langSwitcher.first().click();
      
      // Wait for any language change to take effect
      await page.waitForTimeout(1000);
    }
  });

  test('should not have console errors', async ({ page }) => {
    const consoleErrors: string[] = [];
    
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Filter out known acceptable errors
    const criticalErrors = consoleErrors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('404') &&
      !error.includes('net::ERR_FAILED')
    );
    
    expect(criticalErrors).toHaveLength(0);
  });
});
