/**
 * Entrepreneur Dashboard Page
 * Comprehensive dashboard for entrepreneurs with real data integration
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import { RTLText, RTLFlex } from '../../components/common';
import {
  Lightbulb,
  TrendingUp,
  Users,
  DollarSign,
  Target,
  Calendar,
  BarChart3,
  Plus,
  ArrowRight,
  Activity,
  CheckCircle,
  Clock,
  AlertCircle,
  Zap,
  BookOpen,
  MessageSquare,
  Award
} from 'lucide-react';

// API imports
import { businessIdeasAPI, BusinessIdea } from '../../services/incubatorApi';
import { businessPlansAPI, BusinessPlan } from '../../services/businessPlanApi';
import { businessPlanAnalyticsAPI, DashboardOverview } from '../../services/businessPlanAnalyticsApi';

interface DashboardStats {
  totalIdeas: number;
  totalPlans: number;
  completedPlans: number;
  inProgressPlans: number;
  averageCompletion: number;
  recentActivity: Array<{
    id: string;
    type: 'idea' | 'plan' | 'milestone';
    title: string;
    timestamp: string;
    status?: string;
  }>;
}

const EntrepreneurDashboardPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const { user } = useAppSelector(state => state.auth);

  // State management
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [analyticsOverview, setAnalyticsOverview] = useState<DashboardOverview | null>(null);
  const [recentIdeas, setRecentIdeas] = useState<BusinessIdea[]>([]);
  const [recentPlans, setRecentPlans] = useState<BusinessPlan[]>([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      // Load all dashboard data in parallel
      const [ideasResponse, plansResponse, analyticsResponse] = await Promise.allSettled([
        businessIdeasAPI.getBusinessIdeas(),
        businessPlansAPI.getPlans(),
        businessPlanAnalyticsAPI.getDashboardOverview(30)
      ]);

      // Process business ideas
      const ideas = ideasResponse.status === 'fulfilled' ? ideasResponse.value : [];
      const userIdeas = Array.isArray(ideas) ? ideas.filter(idea => idea.owner?.id === user.id) : [];
      setRecentIdeas(userIdeas.slice(0, 3));

      // Process business plans
      const plans = plansResponse.status === 'fulfilled' ? plansResponse.value : [];
      const userPlans = Array.isArray(plans) ? plans.filter(plan => plan.owner === user.id) : [];
      setRecentPlans(userPlans.slice(0, 3));

      // Process analytics
      if (analyticsResponse.status === 'fulfilled') {
        setAnalyticsOverview(analyticsResponse.value);
      }

      // Calculate dashboard stats
      const completedPlans = userPlans.filter(plan => plan.status === 'completed').length;
      const inProgressPlans = userPlans.filter(plan => plan.status === 'in_progress').length;
      const averageCompletion = userPlans.length > 0 
        ? userPlans.reduce((sum, plan) => sum + (plan.completion_percentage || 0), 0) / userPlans.length 
        : 0;

      // Create recent activity
      const recentActivity = [
        ...userIdeas.slice(0, 2).map(idea => ({
          id: `idea-${idea.id}`,
          type: 'idea' as const,
          title: idea.title,
          timestamp: idea.updated_at || idea.created_at,
          status: idea.status
        })),
        ...userPlans.slice(0, 2).map(plan => ({
          id: `plan-${plan.id}`,
          type: 'plan' as const,
          title: plan.title,
          timestamp: plan.updated_at || plan.created_at,
          status: plan.status
        }))
      ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 5);

      setDashboardStats({
        totalIdeas: userIdeas.length,
        totalPlans: userPlans.length,
        completedPlans,
        inProgressPlans,
        averageCompletion,
        recentActivity
      });

    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400';
      case 'in_progress': return 'text-blue-400';
      case 'draft': return 'text-yellow-400';
      case 'published': return 'text-purple-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'in_progress': return <Clock className="w-4 h-4" />;
      case 'draft': return <AlertCircle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-white mb-2">Error Loading Dashboard</h2>
          <p className="text-gray-400 mb-4">{error}</p>
          <button
            onClick={loadDashboardData}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div>
            <RTLText as="h1" className="text-3xl font-bold text-white">
              {t('dashboard.entrepreneur.welcome', 'Entrepreneur Dashboard')}
            </RTLText>
            <RTLText as="p" className="text-gray-400 mt-2">
              {t('dashboard.entrepreneur.subtitle', 'Build and grow your business ideas')}
            </RTLText>
          </div>
          <div className={`flex items-center space-x-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
            <button
              onClick={() => navigate('/entrepreneur/business-ideas/new')}
              className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-200"
            >
              <Plus className="w-4 h-4" />
              <span>{t('dashboard.createIdea', 'New Idea')}</span>
            </button>
          </div>
        </div>

        {/* Stats Grid */}
        {dashboardStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Ideas */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-gray-400 text-sm">{t('dashboard.stats.totalIdeas', 'Total Ideas')}</p>
                  <p className="text-2xl font-bold text-white">{dashboardStats.totalIdeas}</p>
                </div>
                <Lightbulb className="w-8 h-8 text-yellow-400" />
              </div>
            </div>

            {/* Total Plans */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-gray-400 text-sm">{t('dashboard.stats.totalPlans', 'Business Plans')}</p>
                  <p className="text-2xl font-bold text-white">{dashboardStats.totalPlans}</p>
                </div>
                <BookOpen className="w-8 h-8 text-blue-400" />
              </div>
            </div>

            {/* Completed Plans */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-gray-400 text-sm">{t('dashboard.stats.completed', 'Completed')}</p>
                  <p className="text-2xl font-bold text-green-400">{dashboardStats.completedPlans}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-400" />
              </div>
            </div>

            {/* Average Completion */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div>
                  <p className="text-gray-400 text-sm">{t('dashboard.stats.avgCompletion', 'Avg Completion')}</p>
                  <p className="text-2xl font-bold text-purple-400">{Math.round(dashboardStats.averageCompletion)}%</p>
                </div>
                <BarChart3 className="w-8 h-8 text-purple-400" />
              </div>
            </div>
          </div>
        )}

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Business Ideas */}
          <div className="lg:col-span-2">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <h3 className="text-lg font-semibold text-white">
                  {t('dashboard.recentIdeas', 'Recent Business Ideas')}
                </h3>
                <button
                  onClick={() => navigate('/entrepreneur/business-ideas')}
                  className="text-purple-400 hover:text-purple-300 transition-colors flex items-center space-x-1"
                >
                  <span>{t('dashboard.viewAll', 'View All')}</span>
                  <ArrowRight className="w-4 h-4" />
                </button>
              </div>

              {recentIdeas.length > 0 ? (
                <div className="space-y-4">
                  {recentIdeas.map((idea) => (
                    <div
                      key={idea.id}
                      className="p-4 bg-white/5 rounded-lg border border-white/10 hover:bg-white/10 transition-colors cursor-pointer"
                      onClick={() => navigate(`/entrepreneur/business-ideas/${idea.id}`)}
                    >
                      <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className="flex-1">
                          <h4 className="font-medium text-white mb-1">{idea.title}</h4>
                          <p className="text-gray-400 text-sm line-clamp-2">{idea.description}</p>
                          <div className={`flex items-center space-x-4 mt-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                            <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(idea.status)} bg-current/20`}>
                              {idea.status}
                            </span>
                            <span className="text-xs text-gray-500">
                              {new Date(idea.updated_at || idea.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                        <div className={`flex items-center ${getStatusColor(idea.status)} ${isRTL ? 'mr-4' : 'ml-4'}`}>
                          {getStatusIcon(idea.status)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Lightbulb className="w-12 h-12 text-gray-500 mx-auto mb-4" />
                  <p className="text-gray-400 mb-4">{t('dashboard.noIdeas', 'No business ideas yet')}</p>
                  <button
                    onClick={() => navigate('/entrepreneur/business-ideas/new')}
                    className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    {t('dashboard.createFirstIdea', 'Create Your First Idea')}
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Recent Activity & Quick Actions */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h3 className="text-lg font-semibold text-white mb-4">
                {t('dashboard.quickActions', 'Quick Actions')}
              </h3>
              <div className="space-y-3">
                <button
                  onClick={() => navigate('/entrepreneur/business-ideas/new')}
                  className="w-full flex items-center space-x-3 p-3 bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-lg hover:from-purple-600/30 hover:to-blue-600/30 transition-all duration-200 text-left"
                >
                  <Plus className="w-5 h-5 text-purple-400" />
                  <span className="text-white">{t('dashboard.actions.newIdea', 'New Business Idea')}</span>
                </button>

                <button
                  onClick={() => navigate('/entrepreneur/business-plans/new')}
                  className="w-full flex items-center space-x-3 p-3 bg-gradient-to-r from-blue-600/20 to-green-600/20 rounded-lg hover:from-blue-600/30 hover:to-green-600/30 transition-all duration-200 text-left"
                >
                  <BookOpen className="w-5 h-5 text-blue-400" />
                  <span className="text-white">{t('dashboard.actions.newPlan', 'Create Business Plan')}</span>
                </button>

                <button
                  onClick={() => navigate('/entrepreneur/mentorship')}
                  className="w-full flex items-center space-x-3 p-3 bg-gradient-to-r from-green-600/20 to-yellow-600/20 rounded-lg hover:from-green-600/30 hover:to-yellow-600/30 transition-all duration-200 text-left"
                >
                  <Users className="w-5 h-5 text-green-400" />
                  <span className="text-white">{t('dashboard.actions.findMentor', 'Find Mentor')}</span>
                </button>

                <button
                  onClick={() => navigate('/entrepreneur/funding')}
                  className="w-full flex items-center space-x-3 p-3 bg-gradient-to-r from-yellow-600/20 to-red-600/20 rounded-lg hover:from-yellow-600/30 hover:to-red-600/30 transition-all duration-200 text-left"
                >
                  <DollarSign className="w-5 h-5 text-yellow-400" />
                  <span className="text-white">{t('dashboard.actions.exploreFunding', 'Explore Funding')}</span>
                </button>
              </div>
            </div>

            {/* Recent Activity */}
            {dashboardStats && dashboardStats.recentActivity.length > 0 && (
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {t('dashboard.recentActivity', 'Recent Activity')}
                </h3>
                <div className="space-y-3">
                  {dashboardStats.recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-white/5 transition-colors">
                      <div className={`flex items-center ${getStatusColor(activity.status || 'draft')}`}>
                        {activity.type === 'idea' ? <Lightbulb className="w-4 h-4" /> : <BookOpen className="w-4 h-4" />}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-white text-sm font-medium truncate">{activity.title}</p>
                        <p className="text-gray-400 text-xs">
                          {new Date(activity.timestamp).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Business Plans Section */}
        {recentPlans.length > 0 && (
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h3 className="text-lg font-semibold text-white">
                {t('dashboard.recentPlans', 'Recent Business Plans')}
              </h3>
              <button
                onClick={() => navigate('/entrepreneur/business-plans')}
                className="text-purple-400 hover:text-purple-300 transition-colors flex items-center space-x-1"
              >
                <span>{t('dashboard.viewAll', 'View All')}</span>
                <ArrowRight className="w-4 h-4" />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {recentPlans.map((plan) => (
                <div
                  key={plan.id}
                  className="p-4 bg-white/5 rounded-lg border border-white/10 hover:bg-white/10 transition-colors cursor-pointer"
                  onClick={() => navigate(`/entrepreneur/business-plans/${plan.id}`)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <h4 className="font-medium text-white line-clamp-1">{plan.title}</h4>
                    <div className={`flex items-center ${getStatusColor(plan.status)}`}>
                      {getStatusIcon(plan.status)}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">{t('dashboard.completion', 'Completion')}</span>
                      <span className="text-white">{plan.completion_percentage || 0}%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${plan.completion_percentage || 0}%` }}
                      />
                    </div>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{plan.status_display || plan.status}</span>
                      <span>{new Date(plan.updated_at || plan.created_at).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Analytics Overview */}
        {analyticsOverview && (
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <h3 className="text-lg font-semibold text-white mb-6">
              {t('dashboard.analytics', 'Analytics Overview')}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">
                  {Math.round(analyticsOverview.time_analytics.total_time_seconds / 3600)}h
                </div>
                <div className="text-sm text-gray-400">{t('analytics.timeSpent', 'Time Spent')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">
                  {analyticsOverview.time_analytics.total_sessions}
                </div>
                <div className="text-sm text-gray-400">{t('analytics.sessions', 'Work Sessions')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">
                  {Math.round(analyticsOverview.success_metrics.completion_rate * 100)}%
                </div>
                <div className="text-sm text-gray-400">{t('analytics.successRate', 'Success Rate')}</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default EntrepreneurDashboardPage;
