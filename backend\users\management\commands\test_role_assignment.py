from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from users.serializers.main import UserSerializer
from core.services.unified_role_service import UnifiedRoleService

class Command(BaseCommand):
    help = 'Test role assignment for entrepreneur1 user'

    def handle(self, *args, **options):
        try:
            # Get the entrepreneur1 user
            user = User.objects.get(username='entrepreneur1')
            self.stdout.write(f"Found user: {user.username} (ID: {user.id})")
            
            # Test unified role service
            unified_service = UnifiedRoleService()
            primary_role = unified_service.get_user_primary_role(user)
            self.stdout.write(f"Unified Role Service - Primary role: {primary_role}")
            
            # Test UserSerializer
            serializer = UserSerializer(user)
            user_data = serializer.data
            self.stdout.write(f"UserSerializer - User role: {user_data.get('user_role')}")
            
            # Check role assignments directly
            from users.models import UserRoleAssignment, UserRole
            assignments = UserRoleAssignment.objects.filter(
                user_profile=user.profile,
                is_active=True
            ).select_related('role')

            self.stdout.write(f"Active role assignments:")
            for assignment in assignments:
                self.stdout.write(f"  - Role: '{assignment.role.name}' (Active: {assignment.is_active})")
                self.stdout.write(f"    Role ID: {assignment.role.id}")
                self.stdout.write(f"    Role Display Name: '{assignment.role.display_name}'")

            # Check all roles in database
            self.stdout.write(f"\nAll roles in database:")
            all_roles = UserRole.objects.all()
            for role in all_roles:
                self.stdout.write(f"  - ID: {role.id}, Name: '{role.name}', Display: '{role.display_name}'")

            # Test the API response format
            self.stdout.write(f"\nAPI Response format test:")
            self.stdout.write(f"Full user data: {user_data}")

            # Test role-based routing logic
            from core.services.unified_role_service import UnifiedRoleService
            service = UnifiedRoleService()
            dashboard_route = service.getDashboardRoute(primary_role)
            home_route = service.getHomeRoute(primary_role)
            self.stdout.write(f"\nRouting test:")
            self.stdout.write(f"  - Dashboard route: {dashboard_route}")
            self.stdout.write(f"  - Home route: {home_route}")
                
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR('User entrepreneur1 not found'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error: {str(e)}'))
