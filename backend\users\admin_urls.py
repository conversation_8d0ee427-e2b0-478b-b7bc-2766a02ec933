"""
Admin API URLs
Provides admin-specific API endpoints for frontend compatibility
Consolidates all admin endpoints under /api/admin/ for consistent frontend access
"""

from django.urls import path
from .views.admin_dashboard import (
    AdminUsersView,
    AdminDashboardStatsView,
    AdminSystemHealthView,
    AdminActivityLogsView
)

urlpatterns = [
    # Admin users management
    path('users/', AdminUsersView.as_view(), name='admin-users-list'),

    # Admin dashboard and system endpoints (for frontend compatibility)
    path('dashboard/stats/', AdminDashboardStatsView.as_view(), name='admin-dashboard-stats-compat'),
    path('system/', AdminSystemHealthView.as_view(), name='admin-system-health-compat'),
    path('logs/', AdminActivityLogsView.as_view(), name='admin-activity-logs-compat'),
]
