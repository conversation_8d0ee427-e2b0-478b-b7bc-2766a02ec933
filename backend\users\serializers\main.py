from rest_framework import serializers
from django.contrib.auth.models import User
from django.db.models import Count
from django.utils.html import escape
import logging
from ..models import UserProfile, UserRole, UserRoleAssignment, RoleApplication, UserApproval
from core.services.unified_role_service import unified_role_service

logger = logging.getLogger(__name__)


class UserRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserRole
        fields = ['id', 'name', 'display_name', 'description', 'permission_level', 'is_active', 'requires_approval']


class UserRoleAssignmentSerializer(serializers.ModelSerializer):
    role = UserRoleSerializer(read_only=True)

    class Meta:
        model = UserRoleAssignment
        fields = ['id', 'role', 'assigned_at', 'expires_at', 'is_active', 'notes']


class RoleApplicationSerializer(serializers.ModelSerializer):
    # For reading: return full role object
    requested_role = UserRoleSerializer(read_only=True)
    # For writing: accept role ID
    requested_role_id = serializers.PrimaryKeyRelatedField(
        queryset=UserRole.objects.filter(is_active=True),
        source='requested_role',
        write_only=True
    )
    user = serializers.StringRelatedField(read_only=True)

    # Enhanced user fields (same as UserApprovalSerializer)
    user_full_name = serializers.SerializerMethodField()
    user_email = serializers.SerializerMethodField()
    user_username = serializers.SerializerMethodField()
    days_pending = serializers.SerializerMethodField()

    class Meta:
        model = RoleApplication
        fields = ['id', 'user', 'requested_role', 'requested_role_id', 'motivation', 'qualifications',
                 'experience', 'portfolio_url', 'status', 'created_at', 'reviewed_at', 'admin_notes',
                 # Enhanced user fields
                 'user_full_name', 'user_email', 'user_username', 'days_pending',
                 # Entrepreneur fields
                 'company_name', 'project_stage', 'industry', 'project_description',
                 'funding_needed', 'team_size', 'support_needed', 'previous_experience',
                 # Mentor fields
                 'expertise_areas', 'mentoring_experience', 'availability', 'preferred_communication',
                 # Investor fields
                 'investment_range', 'investment_focus', 'investment_stage', 'portfolio_companies', 'due_diligence_requirements',
                 # Community Member fields
                 'interests', 'goals']
        read_only_fields = ['user', 'status', 'reviewed_at', 'admin_notes']

    def get_user_full_name(self, obj):
        """Get user's full name"""
        if obj.user.first_name and obj.user.last_name:
            return f"{obj.user.first_name} {obj.user.last_name}"
        return obj.user.username

    def get_user_email(self, obj):
        """Get user's email"""
        return obj.user.email

    def get_user_username(self, obj):
        """Get user's username"""
        return obj.user.username

    def get_days_pending(self, obj):
        """Calculate days since application was submitted"""
        if obj.created_at:
            from django.utils import timezone
            delta = timezone.now() - obj.created_at
            return delta.days
        return 0


class UserProfileSerializer(serializers.ModelSerializer):
    completion_percentage = serializers.SerializerMethodField()
    active_roles = serializers.SerializerMethodField()
    highest_permission_level = serializers.SerializerMethodField()

    class Meta:
        model = UserProfile
        fields = ['id', 'bio', 'location', 'birth_date', 'phone_number', 'profile_image',
                  'website', 'linkedin_url', 'twitter_url', 'github_url', 'company',
                  'job_title', 'industry', 'experience_years', 'expertise', 'is_active',
                  'email_notifications', 'marketing_emails', 'profile_visibility',
                  'language', 'portfolio_url', 'motivation', 'qualifications',
                  'created_at', 'updated_at', 'last_activity',
                  'completion_percentage', 'active_roles', 'highest_permission_level']
        read_only_fields = ['created_at', 'updated_at', 'last_activity']

    def get_completion_percentage(self, obj):
        """Calculate profile completion percentage"""
        total_fields = 10
        completed_fields = 0
        
        if obj.bio: completed_fields += 1
        if obj.location: completed_fields += 1
        if obj.birth_date: completed_fields += 1
        if obj.phone_number: completed_fields += 1
        if obj.profile_image: completed_fields += 1
        if obj.website: completed_fields += 1
        if obj.company: completed_fields += 1
        if obj.job_title: completed_fields += 1
        if obj.industry: completed_fields += 1
        if obj.experience_years: completed_fields += 1
        
        return int((completed_fields / total_fields) * 100)

    def get_active_roles(self, obj):
        """Get user's active roles"""
        try:
            assignments = UserRoleAssignment.objects.filter(user_profile=obj, is_active=True)
            roles_data = UserRoleSerializer([assignment.role for assignment in assignments], many=True).data

            # If no role assignments, check requested_role_name or default to user
            if not roles_data:
                # Use requested_role_name if available, otherwise default to user
                role_name = getattr(obj, 'requested_role_name', 'user') or 'user'

                # Map role names to display names and permission levels
                role_mapping = {
                    'user': {'display_name': 'Regular User', 'permission_level': 'read'},
                    'entrepreneur': {'display_name': 'Entrepreneur', 'permission_level': 'write'},
                    'mentor': {'display_name': 'Mentor', 'permission_level': 'write'},
                    'investor': {'display_name': 'Investor', 'permission_level': 'write'},
                    'moderator': {'display_name': 'Moderator', 'permission_level': 'moderate'},
                    'admin': {'display_name': 'Administrator', 'permission_level': 'admin'},

                }

                role_info = role_mapping.get(role_name, role_mapping['user'])
                roles_data = [{
                    'name': role_name,
                    'display_name': role_info['display_name'],
                    'permission_level': role_info['permission_level']
                }]

            return roles_data
        except Exception as e:
            # Fallback to default user role if there are any issues
            return [{
                'name': 'user',
                'display_name': 'Regular User',
                'permission_level': 'read'
            }]

    def get_highest_permission_level(self, obj):
        """Get user's highest permission level"""
        assignments = UserRoleAssignment.objects.filter(user_profile=obj, is_active=True)

        # CONSOLIDATED: Import from unified role configuration
        from core.config.role_config import PERMISSION_HIERARCHY, get_role_permission
        permission_hierarchy = PERMISSION_HIERARCHY

        # If no assignments, use requested_role_name to determine permission level
        if not assignments:
            role_name = getattr(obj, 'requested_role_name', 'user') or 'user'
            return get_role_permission(role_name)

        highest_level = 'read'
        highest_value = 1

        for assignment in assignments:
            level_value = permission_hierarchy.get(assignment.role.permission_level, 1)
            if level_value > highest_value:
                highest_value = level_value
                highest_level = assignment.role.permission_level

        return highest_level


class UserSerializer(serializers.ModelSerializer):
    profile = UserProfileSerializer(read_only=True)
    full_name = serializers.SerializerMethodField()
    is_staff_member = serializers.SerializerMethodField()
    role_assignments = serializers.SerializerMethodField()
    user_role = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'full_name',
                  'is_active', 'is_staff', 'is_superuser', 'date_joined', 'last_login',
                  'profile', 'is_staff_member', 'role_assignments', 'user_role']
        read_only_fields = ['id', 'date_joined', 'last_login', 'is_staff_member']

    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}".strip() or obj.username

    def get_is_staff_member(self, obj):
        """Check if user has any staff-level roles using unified role system"""
        return unified_role_service.has_role(obj, 'admin')

    def get_role_assignments(self, obj):
        """Get user's role assignments"""
        # Temporarily return empty list to avoid database schema issues
        return []

    def get_user_role(self, obj):
        """Get user's primary role for frontend role detection using unified role system"""
        # Use unified role service to get primary role
        primary_role = unified_role_service.get_user_primary_role(obj)
        if primary_role:
            return primary_role

        # Check profile-based roles
        try:
            profile = obj.profile

            # Check active role assignments
            assignments = UserRoleAssignment.objects.filter(user_profile=profile, is_active=True)
            if assignments.exists():
                # Get the highest priority role
                role_priority = {
                    'admin': 5,
                    'moderator': 4,
                    'entrepreneur': 3,
                    'mentor': 3,
                    'investor': 2,
                    'user': 1
                }

                highest_role = 'user'
                highest_priority = 0

                for assignment in assignments:
                    role_name = assignment.role.name
                    priority = role_priority.get(role_name, 0)
                    if priority > highest_priority:
                        highest_priority = priority
                        highest_role = role_name

                return highest_role

            # Check if user has a requested role during registration
            if hasattr(profile, 'requested_role_name') and profile.requested_role_name:
                return profile.requested_role_name

        except UserProfile.DoesNotExist:
            pass

        # Default to regular user
        return 'user'





class EnhancedRegistrationSerializer(serializers.ModelSerializer):
    """Enhanced registration with profile information and role assignment"""
    # Basic user fields
    password = serializers.CharField(write_only=True, min_length=8)
    password_confirm = serializers.CharField(write_only=True)

    # Profile fields
    bio = serializers.CharField(max_length=500, required=False, allow_blank=True)
    location = serializers.CharField(max_length=100, required=False, allow_blank=True)
    company = serializers.CharField(max_length=200, required=False, allow_blank=True)
    job_title = serializers.CharField(max_length=100, required=False, allow_blank=True)
    industry = serializers.CharField(max_length=100, required=False, allow_blank=True)
    website = serializers.URLField(required=False, allow_blank=True)
    linkedin_url = serializers.URLField(required=False, allow_blank=True)
    language = serializers.ChoiceField(choices=[('en', 'English'), ('ar', 'Arabic')], default='en')

    # Role assignment fields
    selected_role = serializers.CharField(max_length=50, required=False, allow_blank=True)
    role_additional_info = serializers.JSONField(required=False, default=dict)

    # Additional contact fields
    phone = serializers.CharField(max_length=20, required=False, allow_blank=True)

    # Role-specific fields - Entrepreneur
    business_name = serializers.CharField(max_length=200, required=False, allow_blank=True)
    business_stage = serializers.CharField(max_length=50, required=False, allow_blank=True)
    funding_needed = serializers.CharField(max_length=50, required=False, allow_blank=True)
    business_description = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    team_size = serializers.CharField(max_length=50, required=False, allow_blank=True)
    support_needed = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    previous_experience = serializers.CharField(max_length=1000, required=False, allow_blank=True)

    # Role-specific fields - Mentor
    expertise = serializers.CharField(max_length=200, required=False, allow_blank=True)
    experience = serializers.CharField(max_length=50, required=False, allow_blank=True)
    mentorship_areas = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    availability = serializers.CharField(max_length=50, required=False, allow_blank=True)
    preferred_communication = serializers.CharField(max_length=50, required=False, allow_blank=True)

    # Role-specific fields - Investor
    investment_range = serializers.CharField(max_length=50, required=False, allow_blank=True)
    investment_stage = serializers.CharField(max_length=50, required=False, allow_blank=True)
    preferred_industries = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    investment_criteria = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    portfolio_companies = serializers.CharField(max_length=1000, required=False, allow_blank=True)

    # Role-specific fields - Community Member/User
    interests = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    goals = serializers.CharField(max_length=1000, required=False, allow_blank=True)

    # General fields (for all roles)
    portfolio_url = serializers.URLField(required=False, allow_blank=True)
    motivation = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    qualifications = serializers.CharField(max_length=1000, required=False, allow_blank=True)

    class Meta:
        model = User
        fields = ['username', 'email', 'first_name', 'last_name', 'password', 'password_confirm'] + [
            'bio', 'location', 'company', 'job_title', 'industry',
            'website', 'linkedin_url', 'language', 'selected_role',
            'role_additional_info', 'phone',
            # Entrepreneur fields
            'business_name', 'business_stage', 'funding_needed', 'business_description',
            'team_size', 'support_needed', 'previous_experience',
            # Mentor fields
            'expertise', 'experience', 'mentorship_areas', 'availability', 'preferred_communication',
            # Investor fields
            'investment_range', 'investment_stage', 'preferred_industries', 'investment_criteria',
            'portfolio_companies',
            # Community Member fields
            'interests', 'goals',
            # General fields
            'portfolio_url', 'motivation', 'qualifications'
        ]

    def validate(self, data):
        """Enhanced validation with detailed error messages and better password strength checking"""
        errors = {}

        # Password confirmation validation
        password = data.get('password', '')
        password_confirm = data.get('password_confirm', '')

        if password != password_confirm:
            errors['password_confirm'] = "Passwords don't match"

        # Enhanced password strength validation
        if len(password) < 8:
            errors['password'] = "Password must be at least 8 characters long"
        else:
            password_errors = []

            # Check for common weak passwords
            weak_passwords = [
                'password', 'password123', '12345678', 'qwerty', 'abc123',
                'admin', 'administrator', 'root', 'user', 'guest', 'test',
                '11111111', '00000000', 'abcdefgh', 'aaaaaaaa'
            ]
            if password.lower() in weak_passwords:
                password_errors.append("Password is too common")

            # Check for at least one letter and one number
            has_letter = any(c.isalpha() for c in password)
            has_number = any(c.isdigit() for c in password)
            has_upper = any(c.isupper() for c in password)
            has_lower = any(c.islower() for c in password)
            has_special = any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in password)

            if not has_letter:
                password_errors.append("must contain at least one letter")
            if not has_number:
                password_errors.append("must contain at least one number")
            if not has_upper:
                password_errors.append("must contain at least one uppercase letter")
            if not has_lower:
                password_errors.append("must contain at least one lowercase letter")

            # Check for sequential characters
            sequential_patterns = ['123', '234', '345', '456', '567', '678', '789', 'abc', 'bcd', 'cde']
            if any(pattern in password.lower() for pattern in sequential_patterns):
                password_errors.append("should not contain sequential characters")

            # Check for repeated characters (more lenient)
            if len(set(password)) < len(password) * 0.4:  # Less than 40% unique characters
                password_errors.append("should not have too many repeated characters")

            if password_errors:
                errors['password'] = f"Password {', '.join(password_errors)}"

        # Username validation
        username = data.get('username', '').strip()
        if username:
            if len(username) < 3:
                errors['username'] = "Username must be at least 3 characters long"
            elif len(username) > 30:
                errors['username'] = "Username must be less than 30 characters"
            elif not username.replace('_', '').replace('-', '').isalnum():
                errors['username'] = "Username can only contain letters, numbers, underscores, and hyphens"
            elif User.objects.filter(username=username).exists():
                errors['username'] = "This username is already taken"

        # Email validation
        email = data.get('email', '').strip()
        if email:
            import re
            email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_regex, email):
                errors['email'] = "Please enter a valid email address"
            elif User.objects.filter(email=email).exists():
                errors['email'] = "This email address is already registered"

        # Name validation
        first_name = data.get('first_name', '').strip()
        last_name = data.get('last_name', '').strip()

        if first_name and len(first_name) < 2:
            errors['first_name'] = "First name must be at least 2 characters long"
        if last_name and len(last_name) < 2:
            errors['last_name'] = "Last name must be at least 2 characters long"

        # URL validation for optional fields - only validate if not empty
        for url_field in ['website', 'linkedin_url', 'portfolio_url']:
            url_value = data.get(url_field, '').strip()
            if url_value:  # Only validate if URL is provided
                import re
                # More permissive URL pattern that accepts localhost and various URL formats
                url_pattern = r'^https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.-]*)?(?:\?(?:[\w&=%.-]*)?)?(?:\#(?:[\w.-]*)?)?)?$'
                if not re.match(url_pattern, url_value):
                    field_name = url_field.replace('_', ' ').title()
                    errors[url_field] = f"{field_name} must be a valid URL starting with http:// or https://"
            else:
                # Clear the field if it's empty to avoid validation issues
                data[url_field] = ''

        # Sanitize text fields to prevent XSS
        text_fields = ['first_name', 'last_name', 'bio', 'location', 'company', 'job_title', 'industry']
        for field in text_fields:
            if field in data and data[field]:
                data[field] = escape(data[field].strip())

        # If there are validation errors, raise them all at once
        if errors:
            raise serializers.ValidationError(errors)

        return data

    def create(self, validated_data):
        # Extract profile data
        profile_data = {
            'bio': validated_data.pop('bio', ''),
            'location': validated_data.pop('location', ''),
            'company': validated_data.pop('company', ''),
            'job_title': validated_data.pop('job_title', ''),
            'industry': validated_data.pop('industry', ''),
            'website': validated_data.pop('website', ''),
            'linkedin_url': validated_data.pop('linkedin_url', ''),
            'language': validated_data.pop('language', 'en'),
            'phone_number': validated_data.pop('phone', ''),  # Map phone to phone_number
        }

        # Extract role assignment data
        selected_role = validated_data.pop('selected_role', '')
        role_additional_info = validated_data.pop('role_additional_info', {})

        # Extract motivation and qualifications from top-level fields
        motivation = validated_data.pop('motivation', '')
        qualifications = validated_data.pop('qualifications', '')

        # Add motivation and qualifications to role_additional_info
        if motivation:
            role_additional_info['motivation'] = motivation
        if qualifications:
            role_additional_info['qualifications'] = qualifications

        # Extract role-specific fields and map them to profile fields where applicable
        role_specific_data = {
            # Entrepreneur fields
            'business_name': validated_data.pop('business_name', ''),
            'business_stage': validated_data.pop('business_stage', ''),
            'funding_needed': validated_data.pop('funding_needed', ''),
            'business_description': validated_data.pop('business_description', ''),
            'team_size': validated_data.pop('team_size', ''),
            'support_needed': validated_data.pop('support_needed', ''),
            'previous_experience': validated_data.pop('previous_experience', ''),
            # Mentor fields
            'expertise': validated_data.pop('expertise', ''),
            'experience': validated_data.pop('experience', ''),  # Keep as experience for consistency
            'mentorship_areas': validated_data.pop('mentorship_areas', ''),
            'availability': validated_data.pop('availability', ''),
            'preferred_communication': validated_data.pop('preferred_communication', ''),
            # Investor fields
            'investment_range': validated_data.pop('investment_range', ''),
            'investment_stage': validated_data.pop('investment_stage', ''),
            'preferred_industries': validated_data.pop('preferred_industries', ''),
            'investment_criteria': validated_data.pop('investment_criteria', ''),
            'portfolio_companies': validated_data.pop('portfolio_companies', ''),
            # Community Member fields
            'interests': validated_data.pop('interests', ''),
            'goals': validated_data.pop('goals', ''),
            # General fields
            'portfolio_url': validated_data.pop('portfolio_url', ''),
        }

        # Create user
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)

        # Set user activation based on role - regular users are active immediately
        if selected_role == 'user' or not selected_role:
            user.is_active = True
        else:
            # Other roles require admin approval
            user.is_active = False
        user.save()

        # Ensure profile exists (get or create to handle signal timing issues)
        from users.models import UserProfile
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={'language': 'en', 'is_active': True}
        )

        # Update profile with additional data and save
        for key, value in profile_data.items():
            setattr(profile, key, value)

        # Store the requested role name in the profile
        if selected_role:
            profile.requested_role_name = selected_role

        # Save motivation and qualifications to profile
        if motivation:
            profile.motivation = motivation
        if qualifications:
            profile.qualifications = qualifications

        # Save role-specific data directly to profile fields with proper field mapping
        for key, value in role_specific_data.items():
            if value:  # Only set if value is not empty
                # Handle field name mappings
                if key == 'experience' and selected_role == 'mentor':
                    # Map 'experience' to 'mentor_experience' for mentors
                    if hasattr(profile, 'mentor_experience'):
                        setattr(profile, 'mentor_experience', value)
                elif key == 'expertise':
                    # Map 'expertise' to the general expertise field
                    if hasattr(profile, 'expertise'):
                        setattr(profile, 'expertise', value)
                elif key == 'preferred_industries' and selected_role == 'investor':
                    # Map 'preferred_industries' to 'preferred_industries' (already correct)
                    if hasattr(profile, key):
                        setattr(profile, key, value)
                elif hasattr(profile, key):
                    setattr(profile, key, value)

        # Also store role-specific data in the profile's role_additional_info field for backward compatibility
        # Merge with existing role_additional_info if any
        existing_role_info = profile.role_additional_info or {}
        existing_role_info.update(role_specific_data)
        profile.role_additional_info = existing_role_info

        profile.save()

        # Handle role assignment for all users
        if selected_role:
            # Import here to avoid circular imports
            from ..models import UserRole, RoleApplication, UserRoleAssignment
            import logging
            logger = logging.getLogger(__name__)

            try:
                # Get or create the role
                user_role, created = UserRole.objects.get_or_create(
                    name=selected_role,
                    defaults={
                        'display_name': selected_role.replace('_', ' ').title(),
                        'description': f'{selected_role.replace("_", " ").title()} role',
                        'permission_level': 'read' if selected_role == 'user' else 'write',
                        'requires_approval': selected_role != 'user'
                    }
                )

                if selected_role == 'user':
                    # For regular users, create role assignment immediately
                    UserRoleAssignment.objects.get_or_create(
                        user_profile=profile,
                        role=user_role,
                        defaults={
                            'is_active': True,
                            'is_approved': True,
                            'assigned_by': None,  # Auto-assigned during registration
                            'notes': 'Auto-assigned during registration'
                        }
                    )
                    logger.info(f"Created role assignment for user {user.username} with role {selected_role}")
                else:
                    # For special roles, create role application for approval
                    # Ensure role_additional_info is a dictionary
                    if not isinstance(role_additional_info, dict):
                        role_additional_info = {}

                    # Create role application with comprehensive role-specific data
                    # Get motivation and qualifications from main data or role_additional_info
                    motivation = (
                        self.initial_data.get('motivation') or
                        role_additional_info.get('motivation') or
                        f'Applied for {selected_role} role during registration'
                    )
                    qualifications = (
                        self.initial_data.get('qualifications') or
                        role_additional_info.get('qualifications') or
                        'Provided during registration process'
                    )

                    role_application_data = {
                        'user': user,
                        'requested_role': user_role,
                        'motivation': motivation,
                        'qualifications': qualifications,
                        'experience': role_specific_data.get('experience', ''),
                        'portfolio_url': role_specific_data.get('portfolio_url', ''),
                        'status': 'pending'
                    }

                    # Add role-specific fields based on the selected role
                    if selected_role == 'entrepreneur':
                        role_application_data.update({
                            'company_name': role_specific_data.get('business_name', ''),
                            'project_stage': role_specific_data.get('business_stage', ''),
                            'industry': role_specific_data.get('industry', ''),
                            'project_description': role_specific_data.get('business_description', ''),
                            'funding_needed': role_specific_data.get('funding_needed', ''),
                            'team_size': role_specific_data.get('team_size', ''),
                            'support_needed': role_specific_data.get('support_needed', ''),
                            'previous_experience': role_specific_data.get('previous_experience', '')
                        })
                    elif selected_role == 'mentor':
                        role_application_data.update({
                            'expertise_areas': role_specific_data.get('expertise', ''),
                            'mentoring_experience': role_specific_data.get('experience', ''),
                            'availability': role_specific_data.get('availability', ''),
                            'preferred_communication': role_specific_data.get('preferred_communication', '')
                        })
                    elif selected_role == 'investor':
                        role_application_data.update({
                            'investment_range': role_specific_data.get('investment_range', ''),
                            'investment_stage': role_specific_data.get('investment_stage', ''),
                            'investment_focus': role_specific_data.get('preferred_industries', ''),
                            'due_diligence_requirements': role_specific_data.get('investment_criteria', ''),
                            'portfolio_companies': role_specific_data.get('portfolio_companies', '')
                        })
                    elif selected_role == 'community_member':
                        role_application_data.update({
                            'interests': role_specific_data.get('interests', ''),
                            'goals': role_specific_data.get('goals', '')
                        })

                    role_application = RoleApplication.objects.create(**role_application_data)
                    logger.info(f"Created role application for user {user.username} requesting {selected_role} role")

                # Store role additional info in profile if provided
                if role_additional_info:
                    profile.role_additional_info = role_additional_info
                    profile.save()

            except Exception as e:
                # Log any errors but don't fail registration
                logger.error(f"Error handling role assignment for user {user.username}: {e}")
                # Still save the role info in profile for manual review
                if role_additional_info:
                    profile.role_additional_info = role_additional_info
                    profile.requested_role_name = selected_role
                    profile.save()

        # Send email verification if email verification is enabled
        try:
            from django.conf import settings
            from ..models.email_verification import EmailVerificationToken

            # Check if email verification is enabled
            enable_email_verification = getattr(settings, 'ENABLE_EMAIL_VERIFICATION', True)

            if enable_email_verification:
                # Create email verification token
                verification_token = EmailVerificationToken.objects.create(
                    user=user,
                    email=user.email
                )

                # Send verification email
                verification_token.send_verification_email()
                logger.info(f"Email verification sent to {user.email}")

        except Exception as e:
            # Log error but don't fail registration
            logger.error(f"Failed to send email verification to {user.email}: {e}")

        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating user information"""
    profile = UserProfileSerializer(required=False)

    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email', 'profile']

    def update(self, instance, validated_data):
        profile_data = validated_data.pop('profile', {})
        
        # Update user fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update profile fields
        if profile_data:
            profile = instance.profile
            for attr, value in profile_data.items():
                setattr(profile, attr, value)
            profile.save()

        return instance


class UserApprovalSerializer(serializers.ModelSerializer):
    """
    Serializer for UserApproval model with enhanced registration data
    """
    user_full_name = serializers.SerializerMethodField()
    user_email = serializers.SerializerMethodField()
    user_username = serializers.SerializerMethodField()
    days_pending = serializers.SerializerMethodField()
    requested_role_info = serializers.SerializerMethodField()
    role_specific_data = serializers.SerializerMethodField()
    profile_summary = serializers.SerializerMethodField()

    class Meta:
        model = UserApproval
        fields = [
            'id', 'user', 'status', 'created_at', 'updated_at',
            'reviewed_by', 'reviewed_at', 'rejection_reason', 'admin_notes',
            # Enhanced fields
            'user_full_name', 'user_email', 'user_username', 'days_pending',
            'requested_role_info', 'role_specific_data', 'profile_summary'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_user_full_name(self, obj):
        """Get user's full name"""
        if obj.user.first_name and obj.user.last_name:
            return f"{obj.user.first_name} {obj.user.last_name}"
        return obj.user.username

    def get_user_email(self, obj):
        """Get user's email"""
        return obj.user.email

    def get_user_username(self, obj):
        """Get user's username"""
        return obj.user.username

    def get_days_pending(self, obj):
        """Calculate days since approval request was created"""
        if obj.status == 'pending':
            from django.utils import timezone
            delta = timezone.now() - obj.created_at
            return delta.days
        return None

    def get_requested_role_info(self, obj):
        """Get information about the requested role"""
        try:
            if hasattr(obj.user, 'profile') and obj.user.profile:
                profile = obj.user.profile

                # Get role applications
                role_applications = RoleApplication.objects.filter(
                    user=obj.user
                ).order_by('-created_at')

                if role_applications.exists():
                    latest_app = role_applications.first()
                    return {
                        'role_name': latest_app.requested_role.name,
                        'role_display_name': latest_app.requested_role.display_name,
                        'application_status': latest_app.status,
                        'motivation': latest_app.motivation,
                        'qualifications': latest_app.qualifications,
                        'applied_at': latest_app.created_at.isoformat() if latest_app.created_at else None
                    }

                # Fallback to profile requested role
                if hasattr(profile, 'requested_role_name') and profile.requested_role_name:
                    return {
                        'role_name': profile.requested_role_name,
                        'role_display_name': profile.requested_role_name.replace('_', ' ').title(),
                        'application_status': 'pending',
                        'motivation': getattr(profile, 'motivation', ''),
                        'qualifications': getattr(profile, 'qualifications', ''),
                        'applied_at': obj.created_at.isoformat() if obj.created_at else None
                    }
        except Exception as e:
            pass

        return None

    def get_role_specific_data(self, obj):
        """Get role-specific data from applications"""
        try:
            role_applications = RoleApplication.objects.filter(
                user=obj.user
            ).order_by('-created_at')

            if role_applications.exists():
                app = role_applications.first()
                role_name = app.requested_role.name

                if role_name == 'entrepreneur':
                    return {
                        'business_name': app.company_name,
                        'business_stage': app.project_stage,
                        'funding_needed': app.funding_needed,
                        'business_description': app.project_description,
                        'industry': app.industry,
                        'team_size': app.team_size,
                        'support_needed': ', '.join(app.support_needed) if isinstance(app.support_needed, list) else app.support_needed,
                        'previous_experience': app.previous_experience,
                        'experience': app.experience,
                        'portfolio_url': app.portfolio_url
                    }
                elif role_name == 'mentor':
                    return {
                        'expertise': ', '.join(app.expertise_areas) if isinstance(app.expertise_areas, list) else app.expertise_areas,
                        'mentor_experience': app.mentoring_experience,
                        'mentorship_areas': ', '.join(app.expertise_areas) if isinstance(app.expertise_areas, list) else app.expertise_areas,
                        'availability': app.availability,
                        'preferred_communication': ', '.join(app.preferred_communication) if isinstance(app.preferred_communication, list) else app.preferred_communication,
                        'experience': app.experience,
                        'portfolio_url': app.portfolio_url
                    }
                elif role_name == 'investor':
                    return {
                        'investment_range': app.investment_range,
                        'investment_stage': ', '.join(app.investment_stage) if isinstance(app.investment_stage, list) else app.investment_stage,
                        'preferred_industries': ', '.join(app.investment_focus) if isinstance(app.investment_focus, list) else app.investment_focus,
                        'investment_criteria': app.due_diligence_requirements,
                        'portfolio_companies': app.portfolio_companies,
                        'experience': app.experience,
                        'portfolio_url': app.portfolio_url
                    }
                elif role_name == 'user' or role_name == 'community_member':
                    return {
                        'interests': getattr(app, 'interests', ''),
                        'goals': getattr(app, 'goals', ''),
                        'experience': app.experience,
                        'portfolio_url': app.portfolio_url
                    }
        except Exception as e:
            pass

        return {}

    def get_profile_summary(self, obj):
        """Get user profile summary"""
        try:
            if hasattr(obj.user, 'profile') and obj.user.profile:
                profile = obj.user.profile
                return {
                    'location': profile.location,
                    'company': profile.company,
                    'job_title': profile.job_title,
                    'industry': profile.industry,
                    'phone_number': profile.phone_number,
                    'bio': profile.bio,
                    'language': profile.language,
                    'website': profile.website,
                    'linkedin_url': profile.linkedin_url,
                    'experience_years': profile.experience_years,
                    'created_at': profile.created_at.isoformat() if profile.created_at else None
                }
        except Exception as e:
            pass

        return {}
