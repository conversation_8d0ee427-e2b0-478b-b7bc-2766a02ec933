/**
 * Entrepreneur Business Ideas Page
 * Dedicated page for entrepreneurs to manage their business ideas
 * Matches the entrepreneur dashboard styling and functionality
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import { useCRUD } from '../../hooks/useCRUD';
import { RTLText, RTLFlex } from '../../components/common';
import { BusinessIdea, businessIdeasAPI } from '../../services/incubatorApi';
import BusinessIdeaForm from '../../components/incubator/forms/BusinessIdeaForm';
import BusinessIdeaViewModal from '../../components/incubator/BusinessIdeaViewModal';
import {
  Lightbulb,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Calendar,
  TrendingUp,
  Users,
  DollarSign,
  CheckCircle,
  Clock,
  AlertCircle,
  RefreshCw,
  ArrowRight,
  MoreHorizontal
} from 'lucide-react';

const EntrepreneurBusinessIdeasPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  const navigate = useNavigate();
  const location = useLocation();

  // State management
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedIdea, setSelectedIdea] = useState<BusinessIdea | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // CRUD operations for business ideas
  const businessIdeasCRUD = useCRUD({
    create: async (data: Partial<BusinessIdea>) => {
      if (!user) throw new Error('User not authenticated');
      return businessIdeasAPI.createBusinessIdea({
        ...data,
        owner_id: user.id
      });
    },
    read: () => businessIdeasAPI.getBusinessIdeas(),
    update: (id: number, data: Partial<BusinessIdea>) => 
      businessIdeasAPI.updateBusinessIdea(id, data),
    delete: (id: number) => businessIdeasAPI.deleteBusinessIdea(id)
  }, {
    onSuccess: (operation) => {
      if (operation === 'create') {
        setShowCreateForm(false);
      } else if (operation === 'update') {
        setShowEditForm(false);
        setSelectedIdea(null);
      } else if (operation === 'delete') {
        setShowDeleteDialog(false);
        setSelectedIdea(null);
      }
    }
  });

  // Load data on component mount
  useEffect(() => {
    businessIdeasCRUD.readItems();
  }, []);

  // Handle /new route - automatically open create form
  useEffect(() => {
    if (location.pathname.endsWith('/new')) {
      setShowCreateForm(true);
    }
  }, [location.pathname]);

  // Filter to show only user's ideas
  const userBusinessIdeas = businessIdeasCRUD.data.filter(idea => 
    idea.owner.id === user?.id
  );

  // Apply search and filter
  const filteredIdeas = userBusinessIdeas.filter(idea => {
    const matchesSearch = idea.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         idea.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || idea.current_stage === filterStatus;
    return matchesSearch && matchesFilter;
  });

  // Status color helper
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'concept': return 'text-blue-400';
      case 'validation': return 'text-yellow-400';
      case 'development': return 'text-orange-400';
      case 'scaling': return 'text-green-400';
      case 'established': return 'text-purple-400';
      default: return 'text-gray-400';
    }
  };

  // Status icon helper
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'concept': return <Lightbulb className="w-4 h-4" />;
      case 'validation': return <TrendingUp className="w-4 h-4" />;
      case 'development': return <Users className="w-4 h-4" />;
      case 'scaling': return <DollarSign className="w-4 h-4" />;
      case 'established': return <CheckCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  // Handle actions
  const handleCreate = async (data: Partial<BusinessIdea>) => {
    return await businessIdeasCRUD.createItem(data);
  };

  const handleEdit = (idea: BusinessIdea) => {
    setSelectedIdea(idea);
    setShowEditForm(true);
  };

  const handleUpdate = async (data: Partial<BusinessIdea>) => {
    if (!selectedIdea) return false;
    return await businessIdeasCRUD.updateItem(selectedIdea.id, data);
  };

  const handleDelete = (idea: BusinessIdea) => {
    setSelectedIdea(idea);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (!selectedIdea) return;
    await businessIdeasCRUD.deleteItem(selectedIdea.id);
  };

  const handleView = (idea: BusinessIdea) => {
    setSelectedIdea(idea);
    setShowViewModal(true);
  };

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div>
            <RTLText as="h1" className="text-3xl font-bold text-white">
              {t('entrepreneur.businessIdeas.title', 'My Business Ideas')}
            </RTLText>
            <RTLText as="p" className="text-gray-400 mt-2">
              {t('entrepreneur.businessIdeas.subtitle', 'Manage and develop your innovative business concepts')}
            </RTLText>
          </div>
          <div className={`flex items-center space-x-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
            <button
              onClick={() => businessIdeasCRUD.readItems()}
              disabled={businessIdeasCRUD.isLoading}
              className="p-2 bg-white/10 text-white rounded-lg hover:bg-white/20 transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-5 h-5 ${businessIdeasCRUD.isLoading ? 'animate-spin' : ''}`} />
            </button>
            <button
              onClick={() => setShowCreateForm(true)}
              className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-200"
            >
              <Plus className="w-5 h-5" />
              <span>{t('entrepreneur.businessIdeas.createNew', 'New Idea')}</span>
            </button>
          </div>
        </div>

        {/* Search and Filter Bar */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <div className={`flex flex-col md:flex-row gap-4 ${isRTL ? 'md:flex-row-reverse' : ''}`}>
            {/* Search */}
            <div className="flex-1 relative">
              <Search className={`absolute top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 ${isRTL ? 'right-3' : 'left-3'}`} />
              <input
                type="text"
                placeholder={t('entrepreneur.businessIdeas.searchPlaceholder', 'Search your business ideas...')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full bg-white/10 border border-white/20 rounded-lg py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'}`}
              />
            </div>

            {/* Filter */}
            <div className="relative">
              <Filter className={`absolute top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 ${isRTL ? 'right-3' : 'left-3'}`} />
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className={`bg-white/10 border border-white/20 rounded-lg py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'}`}
              >
                <option value="all">{t('entrepreneur.businessIdeas.allStages', 'All Stages')}</option>
                <option value="concept">{t('entrepreneur.businessIdeas.concept', 'Concept')}</option>
                <option value="validation">{t('entrepreneur.businessIdeas.validation', 'Validation')}</option>
                <option value="development">{t('entrepreneur.businessIdeas.development', 'Development')}</option>
                <option value="scaling">{t('entrepreneur.businessIdeas.scaling', 'Scaling')}</option>
                <option value="established">{t('entrepreneur.businessIdeas.established', 'Established')}</option>
              </select>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <p className="text-gray-400 text-sm">{t('entrepreneur.businessIdeas.totalIdeas', 'Total Ideas')}</p>
                <p className="text-2xl font-bold text-white">{userBusinessIdeas.length}</p>
              </div>
              <Lightbulb className="w-8 h-8 text-yellow-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <p className="text-gray-400 text-sm">{t('entrepreneur.businessIdeas.inDevelopment', 'In Development')}</p>
                <p className="text-2xl font-bold text-orange-400">
                  {userBusinessIdeas.filter(idea => idea.current_stage === 'development').length}
                </p>
              </div>
              <Users className="w-8 h-8 text-orange-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <p className="text-gray-400 text-sm">{t('entrepreneur.businessIdeas.scaling', 'Scaling')}</p>
                <p className="text-2xl font-bold text-green-400">
                  {userBusinessIdeas.filter(idea => idea.current_stage === 'scaling').length}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <p className="text-gray-400 text-sm">{t('entrepreneur.businessIdeas.established', 'Established')}</p>
                <p className="text-2xl font-bold text-purple-400">
                  {userBusinessIdeas.filter(idea => idea.current_stage === 'established').length}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-purple-400" />
            </div>
          </div>
        </div>

        {/* Business Ideas Grid */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          {businessIdeasCRUD.isLoading ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="w-8 h-8 text-purple-400 animate-spin" />
              <span className="ml-3 text-gray-400">{t('common.loading', 'Loading...')}</span>
            </div>
          ) : filteredIdeas.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredIdeas.map((idea) => (
                <div
                  key={idea.id}
                  className="p-6 bg-white/5 rounded-lg border border-white/10 hover:bg-white/10 transition-all duration-200 group"
                >
                  {/* Idea Header */}
                  <div className={`flex items-start justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className="flex-1">
                      <h3 className="font-semibold text-white mb-2 line-clamp-2">{idea.title}</h3>
                      <div className={`flex items-center space-x-2 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                        <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(idea.current_stage)} bg-current/20`}>
                          {idea.current_stage}
                        </span>
                        <span className="text-xs text-gray-500">
                          {new Date(idea.updated_at || idea.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>

                    {/* Actions Dropdown */}
                    <div className="relative group">
                      <button className="p-2 text-gray-400 hover:text-white transition-colors opacity-0 group-hover:opacity-100">
                        <MoreHorizontal className="w-4 h-4" />
                      </button>
                      <div className="absolute right-0 top-full mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                        <div className="py-2">
                          <button
                            onClick={() => handleView(idea)}
                            className="w-full px-4 py-2 text-left text-gray-300 hover:bg-gray-700 hover:text-white transition-colors flex items-center space-x-2"
                          >
                            <Eye className="w-4 h-4" />
                            <span>{t('common.view', 'View')}</span>
                          </button>
                          <button
                            onClick={() => handleEdit(idea)}
                            className="w-full px-4 py-2 text-left text-gray-300 hover:bg-gray-700 hover:text-white transition-colors flex items-center space-x-2"
                          >
                            <Edit className="w-4 h-4" />
                            <span>{t('common.edit', 'Edit')}</span>
                          </button>
                          <button
                            onClick={() => navigate(`/entrepreneur/business-plans/new?ideaId=${idea.id}`)}
                            className="w-full px-4 py-2 text-left text-gray-300 hover:bg-gray-700 hover:text-white transition-colors flex items-center space-x-2"
                          >
                            <ArrowRight className="w-4 h-4" />
                            <span>{t('entrepreneur.businessIdeas.createPlan', 'Create Plan')}</span>
                          </button>
                          <hr className="my-2 border-gray-700" />
                          <button
                            onClick={() => handleDelete(idea)}
                            className="w-full px-4 py-2 text-left text-red-400 hover:bg-red-900/20 hover:text-red-300 transition-colors flex items-center space-x-2"
                          >
                            <Trash2 className="w-4 h-4" />
                            <span>{t('common.delete', 'Delete')}</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Idea Description */}
                  <p className="text-gray-400 text-sm mb-4 line-clamp-3">{idea.description}</p>

                  {/* Idea Metrics */}
                  <div className={`flex items-center justify-between text-xs text-gray-500 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex items-center space-x-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                      <div className={`flex items-center space-x-1 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                        {getStatusIcon(idea.current_stage)}
                        <span>{idea.current_stage}</span>
                      </div>
                      {idea.progress_count && (
                        <div className={`flex items-center space-x-1 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                          <TrendingUp className="w-3 h-3" />
                          <span>{idea.progress_count} updates</span>
                        </div>
                      )}
                    </div>
                    <div className={`flex items-center space-x-1 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                      <Calendar className="w-3 h-3" />
                      <span>{new Date(idea.created_at).toLocaleDateString()}</span>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className={`flex items-center space-x-2 mt-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                    <button
                      onClick={() => handleView(idea)}
                      className="flex-1 px-3 py-2 bg-purple-600/20 text-purple-400 rounded-lg hover:bg-purple-600/30 transition-colors text-sm"
                    >
                      {t('common.view', 'View')}
                    </button>
                    <button
                      onClick={() => handleEdit(idea)}
                      className="flex-1 px-3 py-2 bg-blue-600/20 text-blue-400 rounded-lg hover:bg-blue-600/30 transition-colors text-sm"
                    >
                      {t('common.edit', 'Edit')}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Lightbulb className="w-16 h-16 text-gray-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">
                {searchTerm || filterStatus !== 'all'
                  ? t('entrepreneur.businessIdeas.noResults', 'No ideas match your search')
                  : t('entrepreneur.businessIdeas.noIdeas', 'No business ideas yet')
                }
              </h3>
              <p className="text-gray-400 mb-6">
                {searchTerm || filterStatus !== 'all'
                  ? t('entrepreneur.businessIdeas.tryDifferentSearch', 'Try adjusting your search or filter criteria')
                  : t('entrepreneur.businessIdeas.createFirstIdea', 'Create your first business idea to get started')
                }
              </p>
              {(!searchTerm && filterStatus === 'all') && (
                <button
                  onClick={() => setShowCreateForm(true)}
                  className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-200"
                >
                  {t('entrepreneur.businessIdeas.createNew', 'Create Your First Idea')}
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Create Form Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto border border-white/20">
            <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h2 className="text-xl font-semibold text-white">
                {t('entrepreneur.businessIdeas.createNew', 'Create New Business Idea')}
              </h2>
              <button
                onClick={() => setShowCreateForm(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                ×
              </button>
            </div>
            <BusinessIdeaForm
              onSubmit={handleCreate}
              onCancel={() => setShowCreateForm(false)}
              isSubmitting={businessIdeasCRUD.isLoading}
              mode="create"
            />
          </div>
        </div>
      )}

      {/* Edit Form Modal */}
      {showEditForm && selectedIdea && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto border border-white/20">
            <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h2 className="text-xl font-semibold text-white">
                {t('entrepreneur.businessIdeas.editIdea', 'Edit Business Idea')}
              </h2>
              <button
                onClick={() => {
                  setShowEditForm(false);
                  setSelectedIdea(null);
                }}
                className="text-gray-400 hover:text-white transition-colors"
              >
                ×
              </button>
            </div>
            <BusinessIdeaForm
              initialData={selectedIdea}
              onSubmit={handleUpdate}
              onCancel={() => {
                setShowEditForm(false);
                setSelectedIdea(null);
              }}
              isSubmitting={businessIdeasCRUD.isLoading}
              mode="edit"
            />
          </div>
        </div>
      )}

      {/* View Modal */}
      {showViewModal && selectedIdea && (
        <BusinessIdeaViewModal
          idea={selectedIdea}
          isOpen={showViewModal}
          onClose={() => {
            setShowViewModal(false);
            setSelectedIdea(null);
          }}
          onEdit={() => {
            setShowViewModal(false);
            setShowEditForm(true);
          }}
          onDelete={() => {
            setShowViewModal(false);
            handleDelete(selectedIdea);
          }}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && selectedIdea && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-xl p-6 w-full max-w-md border border-white/20">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">
                {t('entrepreneur.businessIdeas.confirmDelete', 'Delete Business Idea')}
              </h3>
              <p className="text-gray-400 mb-6">
                {t('entrepreneur.businessIdeas.deleteWarning', 'Are you sure you want to delete this business idea? This action cannot be undone.')}
              </p>
              <div className={`flex items-center space-x-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                <button
                  onClick={() => {
                    setShowDeleteDialog(false);
                    setSelectedIdea(null);
                  }}
                  className="flex-1 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={confirmDelete}
                  disabled={businessIdeasCRUD.isLoading}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                >
                  {businessIdeasCRUD.isLoading ? t('common.deleting', 'Deleting...') : t('common.delete', 'Delete')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default EntrepreneurBusinessIdeasPage;
