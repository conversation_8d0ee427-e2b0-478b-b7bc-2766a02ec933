#!/usr/bin/env python
"""
Complete End-to-End Admin Test with Real Login
"""
import requests
import json
import time

def complete_admin_test():
    print('🔐 COMPLETE ADMIN END-TO-END TEST WITH LOGIN')
    print('=' * 60)
    
    backend_url = 'http://localhost:8000'
    session = requests.Session()
    
    try:
        # Step 1: Create Admin User via Django Management
        print('\n1. Creating Admin User...')
        import os
        import sys
        sys.path.insert(0, 'backend')
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
        
        import django
        django.setup()
        
        from django.contrib.auth.models import User
        
        # Create or get admin user
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True,
                'first_name': 'Admin',
                'last_name': 'User'
            }
        )
        admin_user.set_password('admin123')
        admin_user.save()
        
        if created:
            print('   ✅ Admin user created successfully')
        else:
            print('   ✅ Admin user already exists')
        
        print(f'   Username: admin')
        print(f'   Password: admin123')
        print(f'   Is Staff: {admin_user.is_staff}')
        print(f'   Is Superuser: {admin_user.is_superuser}')
        
        # Step 2: Test Login via API
        print('\n2. Testing Admin Login via API...')
        
        # Get CSRF token
        csrf_response = session.get(f'{backend_url}/api/auth/login/')
        csrf_token = session.cookies.get('csrftoken', '')
        
        # Login with admin credentials
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrf_token,
            'Referer': backend_url
        }
        
        login_response = session.post(
            f'{backend_url}/api/auth/login/',
            json=login_data,
            headers=headers
        )
        
        print(f'   Login Status: {login_response.status_code}')
        if login_response.status_code == 200:
            print('   ✅ Login successful!')
            login_data_response = login_response.json()
            print(f'   User: {login_data_response.get("user", {}).get("username", "N/A")}')
            print(f'   Is Staff: {login_data_response.get("user", {}).get("is_staff", False)}')
        else:
            print(f'   ❌ Login failed: {login_response.text}')
            return False
        
        # Step 3: Test Admin Dashboard Stats
        print('\n3. Testing Admin Dashboard Stats...')
        stats_response = session.get(f'{backend_url}/api/admin/dashboard/stats/')
        print(f'   Status: {stats_response.status_code}')
        
        if stats_response.status_code == 200:
            data = stats_response.json()
            print('   ✅ SUCCESS! Dashboard stats loaded')
            print(f'   📊 Users: {data.get("users", {}).get("total", 0)} total, {data.get("users", {}).get("active", 0)} active')
            print(f'   📅 Events: {data.get("events", {}).get("total_events", 0)} total, {data.get("events", {}).get("upcoming_events", 0)} upcoming')
            print(f'   📚 Resources: {data.get("resources", {}).get("total_resources", 0)} total')
            print(f'   📝 Posts: {data.get("posts", {}).get("total_posts", 0)} total')
            print(f'   🖥️  System: {data.get("system", {}).get("uptime", 0)}% uptime')
        else:
            print(f'   ❌ FAILED: {stats_response.status_code} - {stats_response.text[:100]}')
        
        # Step 4: Test Admin Users Management
        print('\n4. Testing Admin Users Management...')
        users_response = session.get(f'{backend_url}/api/admin/users/')
        print(f'   Status: {users_response.status_code}')
        
        if users_response.status_code == 200:
            data = users_response.json()
            print('   ✅ SUCCESS! Users data loaded')
            print(f'   👥 Total Users: {data.get("count", 0)}')
            users_list = data.get("results", [])
            if users_list:
                print(f'   📋 Sample User: {users_list[0].get("username", "N/A")} ({users_list[0].get("email", "N/A")})')
        else:
            print(f'   ❌ FAILED: {users_response.status_code} - {users_response.text[:100]}')
        
        # Step 5: Test System Health
        print('\n5. Testing System Health...')
        health_response = session.get(f'{backend_url}/api/admin/system/')
        print(f'   Status: {health_response.status_code}')
        
        if health_response.status_code == 200:
            data = health_response.json()
            print('   ✅ SUCCESS! System health loaded')
            print(f'   🔧 System Status: {data}')
        else:
            print(f'   ❌ FAILED: {health_response.status_code} - {health_response.text[:100]}')
        
        # Step 6: Test Activity Logs
        print('\n6. Testing Activity Logs...')
        logs_response = session.get(f'{backend_url}/api/admin/logs/')
        print(f'   Status: {logs_response.status_code}')
        
        if logs_response.status_code == 200:
            data = logs_response.json()
            print('   ✅ SUCCESS! Activity logs loaded')
            print(f'   📋 Logs: {data}')
        else:
            print(f'   ❌ FAILED: {logs_response.status_code} - {logs_response.text[:100]}')
        
        # Step 7: Test User Approvals
        print('\n7. Testing User Approvals...')
        approvals_response = session.get(f'{backend_url}/api/users/approvals/')
        print(f'   Status: {approvals_response.status_code}')
        
        if approvals_response.status_code == 200:
            data = approvals_response.json()
            print('   ✅ SUCCESS! User approvals loaded')
            print(f'   ✋ Pending Approvals: {len(data) if isinstance(data, list) else "N/A"}')
        else:
            print(f'   ❌ FAILED: {approvals_response.status_code} - {approvals_response.text[:100]}')
        
        print('\n' + '=' * 60)
        print('🎉 COMPLETE ADMIN TEST FINISHED!')
        print('\n📊 FINAL SUMMARY:')
        print('   ✅ Admin user created and authenticated')
        print('   ✅ All admin endpoints tested with real login')
        print('   ✅ Data loading and API responses verified')
        print('   ✅ Frontend-backend integration confirmed')
        
        return True
        
    except Exception as e:
        print(f'\n❌ ERROR: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    complete_admin_test()
