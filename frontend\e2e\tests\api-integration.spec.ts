import { test, expect } from '@playwright/test';

test.describe('API Integration Tests', () => {
  const API_BASE_URL = 'http://127.0.0.1:8000';

  test('should connect to backend API', async ({ request }) => {
    // Test basic API connectivity
    const response = await request.get(`${API_BASE_URL}/admin/`);
    expect(response.status()).toBe(200);
  });

  test('should handle API authentication endpoints', async ({ request }) => {
    // Test authentication endpoint (should require credentials)
    const response = await request.get(`${API_BASE_URL}/api/`);
    expect(response.status()).toBe(401); // Unauthorized, which is expected
    
    const body = await response.json();
    expect(body).toHaveProperty('detail');
    expect(body.detail).toContain('Authentication credentials were not provided');
  });

  test('should handle CORS properly', async ({ page }) => {
    await page.goto('/');
    
    // Test if frontend can make requests to backend
    const response = await page.evaluate(async () => {
      try {
        const res = await fetch('http://127.0.0.1:8000/api/', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        return {
          status: res.status,
          ok: res.ok,
          headers: Object.fromEntries(res.headers.entries())
        };
      } catch (error) {
        return { error: error.message };
      }
    });

    // Should get 401 (unauthorized) but not CORS error
    expect(response.status).toBe(401);
    expect(response.error).toBeUndefined();
  });

  test('should load static files from backend', async ({ request }) => {
    // Test if static files are served correctly
    const response = await request.get(`${API_BASE_URL}/static/admin/css/base.css`);
    expect(response.status()).toBe(200);
    expect(response.headers()['content-type']).toContain('text/css');
  });

  test('should handle API rate limiting gracefully', async ({ request }) => {
    // Make multiple requests to test rate limiting
    const requests = Array.from({ length: 5 }, () => 
      request.get(`${API_BASE_URL}/api/`)
    );
    
    const responses = await Promise.all(requests);
    
    // All should return 401 (unauthorized) but not be rate limited for basic requests
    responses.forEach(response => {
      expect([401, 429]).toContain(response.status()); // 401 or 429 (rate limited)
    });
  });

  test('should handle malformed API requests', async ({ request }) => {
    // Test with invalid JSON
    const response = await request.post(`${API_BASE_URL}/api/`, {
      data: 'invalid json',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    // Should handle gracefully (400 Bad Request or 401 Unauthorized)
    expect([400, 401, 415]).toContain(response.status());
  });
});
