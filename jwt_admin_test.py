#!/usr/bin/env python
"""
Complete Admin Test with JWT Authentication
"""
import requests
import json
import time

def jwt_admin_test():
    print('🔐 COMPLETE ADMIN TEST WITH JWT AUTHENTICATION')
    print('=' * 60)
    
    backend_url = 'http://localhost:8000'
    
    try:
        # Step 1: Create Admin User
        print('\n1. Creating Admin User...')
        import os
        import sys
        sys.path.insert(0, 'backend')
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
        
        import django
        django.setup()
        
        from django.contrib.auth.models import User
        
        # Create or get admin user
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True,
                'first_name': 'Admin',
                'last_name': 'User'
            }
        )
        admin_user.set_password('admin123')
        admin_user.save()
        
        print(f'   ✅ Admin user ready: {admin_user.username}')
        print(f'   Is Staff: {admin_user.is_staff}')
        print(f'   Is Superuser: {admin_user.is_superuser}')
        
        # Step 2: Get JWT Token
        print('\n2. Getting JWT Token...')
        
        token_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        # Try different JWT endpoints
        jwt_endpoints = [
            '/api/auth/token/',
            '/api/auth/jwt/create/',
            '/api/token/',
            '/api/auth/login/'
        ]
        
        access_token = None
        
        for endpoint in jwt_endpoints:
            try:
                token_response = requests.post(
                    f'{backend_url}{endpoint}',
                    json=token_data,
                    headers={'Content-Type': 'application/json'}
                )
                print(f'   Trying {endpoint}: {token_response.status_code}')
                
                if token_response.status_code == 200:
                    token_data_response = token_response.json()
                    access_token = token_data_response.get('access') or token_data_response.get('token')
                    if access_token:
                        print(f'   ✅ JWT Token obtained from {endpoint}')
                        break
                    else:
                        print(f'   Response: {token_data_response}')
            except Exception as e:
                print(f'   Error with {endpoint}: {e}')
        
        if not access_token:
            print('   ⚠️  JWT token not found, trying session-based auth...')
            
            # Try session-based authentication
            session = requests.Session()
            
            # Get CSRF token
            csrf_response = session.get(f'{backend_url}/api/auth/login/')
            csrf_token = session.cookies.get('csrftoken', '')
            
            # Login with session
            login_response = session.post(
                f'{backend_url}/api/auth/login/',
                json=token_data,
                headers={
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrf_token,
                    'Referer': backend_url
                }
            )
            
            if login_response.status_code == 200:
                print('   ✅ Session authentication successful')
                
                # Test with session
                headers = {}
                test_session = session
            else:
                print(f'   ❌ Session auth failed: {login_response.status_code}')
                return False
        else:
            # Use JWT token
            headers = {'Authorization': f'Bearer {access_token}'}
            test_session = requests.Session()
            test_session.headers.update(headers)
        
        # Step 3: Test Admin Dashboard Stats
        print('\n3. Testing Admin Dashboard Stats...')
        stats_response = test_session.get(f'{backend_url}/api/admin/dashboard/stats/')
        print(f'   Status: {stats_response.status_code}')
        
        if stats_response.status_code == 200:
            data = stats_response.json()
            print('   ✅ SUCCESS! Dashboard stats loaded')
            print(f'   📊 Users: {data.get("users", {}).get("total", 0)} total, {data.get("users", {}).get("active", 0)} active')
            print(f'   📅 Events: {data.get("events", {}).get("total_events", 0)} total')
            print(f'   📚 Resources: {data.get("resources", {}).get("total_resources", 0)} total')
            print(f'   📝 Posts: {data.get("posts", {}).get("total_posts", 0)} total')
        else:
            print(f'   ❌ FAILED: {stats_response.status_code}')
            print(f'   Response: {stats_response.text[:200]}')
        
        # Step 4: Test Admin Users Management
        print('\n4. Testing Admin Users Management...')
        users_response = test_session.get(f'{backend_url}/api/admin/users/')
        print(f'   Status: {users_response.status_code}')
        
        if users_response.status_code == 200:
            data = users_response.json()
            print('   ✅ SUCCESS! Users data loaded')
            print(f'   👥 Total Users: {data.get("count", 0)}')
            users_list = data.get("results", [])
            if users_list:
                print(f'   📋 Sample User: {users_list[0].get("username", "N/A")}')
        else:
            print(f'   ❌ FAILED: {users_response.status_code}')
            print(f'   Response: {users_response.text[:200]}')
        
        # Step 5: Test System Health
        print('\n5. Testing System Health...')
        health_response = test_session.get(f'{backend_url}/api/admin/system/')
        print(f'   Status: {health_response.status_code}')
        
        if health_response.status_code == 200:
            data = health_response.json()
            print('   ✅ SUCCESS! System health loaded')
            print(f'   🔧 System Data: {json.dumps(data, indent=2)[:200]}...')
        else:
            print(f'   ❌ FAILED: {health_response.status_code}')
            print(f'   Response: {health_response.text[:200]}')
        
        # Step 6: Test Activity Logs
        print('\n6. Testing Activity Logs...')
        logs_response = test_session.get(f'{backend_url}/api/admin/logs/')
        print(f'   Status: {logs_response.status_code}')
        
        if logs_response.status_code == 200:
            data = logs_response.json()
            print('   ✅ SUCCESS! Activity logs loaded')
            print(f'   📋 Logs Data: {json.dumps(data, indent=2)[:200]}...')
        else:
            print(f'   ❌ FAILED: {logs_response.status_code}')
            print(f'   Response: {logs_response.text[:200]}')
        
        print('\n' + '=' * 60)
        print('🎉 COMPLETE ADMIN TEST WITH AUTHENTICATION FINISHED!')
        
        return True
        
    except Exception as e:
        print(f'\n❌ ERROR: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    jwt_admin_test()
