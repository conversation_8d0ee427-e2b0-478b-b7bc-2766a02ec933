#!/usr/bin/env python3
"""
Simple API Test for Entrepreneur Endpoints
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_api():
    print("🚀 Testing Entrepreneur APIs")
    print("=" * 50)
    
    # Test 1: Server Health
    try:
        response = requests.get(f"{BASE_URL}/admin/", timeout=5)
        print(f"✅ Server Health: {response.status_code}")
    except Exception as e:
        print(f"❌ Server Health: {e}")
        return
    
    # Test 2: API Root
    try:
        response = requests.get(f"{BASE_URL}/api/", timeout=5)
        print(f"✅ API Root: {response.status_code}")
    except Exception as e:
        print(f"❌ API Root: {e}")
    
    # Test 3: Business Ideas (without auth)
    try:
        response = requests.get(f"{BASE_URL}/api/incubator/business-ideas/", timeout=5)
        print(f"📊 Business Ideas: {response.status_code}")
        if response.status_code == 401:
            print("   (Authentication required - expected)")
    except Exception as e:
        print(f"❌ Business Ideas: {e}")
    
    # Test 4: Try to login with admin
    try:
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post(f"{BASE_URL}/api/auth/login/", json=login_data, timeout=5)
        print(f"🔐 Admin Login: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access') or data.get('access_token')
            if token:
                print(f"   Token received: {token[:20]}...")
                
                # Test authenticated request
                headers = {'Authorization': f'Bearer {token}'}
                auth_response = requests.get(f"{BASE_URL}/api/incubator/business-ideas/", 
                                           headers=headers, timeout=5)
                print(f"📊 Business Ideas (Auth): {auth_response.status_code}")
        else:
            print(f"   Response: {response.text[:100]}")
            
    except Exception as e:
        print(f"❌ Admin Login: {e}")
    
    # Test 5: Try token endpoint
    try:
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post(f"{BASE_URL}/api/auth/token/", json=login_data, timeout=5)
        print(f"🎫 Token Endpoint: {response.status_code}")
        if response.status_code == 200:
            print("   Token endpoint working!")
    except Exception as e:
        print(f"❌ Token Endpoint: {e}")
    
    print("\n✅ API Test Complete!")

if __name__ == "__main__":
    test_api()
