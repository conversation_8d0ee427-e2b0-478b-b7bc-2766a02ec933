#!/usr/bin/env python3
"""
Frontend Implementation Test for Entrepreneur Business Ideas Fix
Tests the implementation without requiring a running backend
"""

import os
import re
import json
from pathlib import Path

class EntrepreneurFrontendTester:
    def __init__(self):
        self.test_results = []
        self.frontend_path = Path("frontend/src")
        
    def log_test(self, test_name, success, details=""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")
        self.test_results.append({
            'test': test_name,
            'success': success,
            'details': details
        })
        return success
    
    def test_file_exists(self, file_path, description):
        """Test if a file exists"""
        full_path = self.frontend_path / file_path
        exists = full_path.exists()
        self.log_test(f"File Exists: {description}", exists, str(full_path))
        return exists
    
    def test_file_contains(self, file_path, pattern, description):
        """Test if a file contains a specific pattern"""
        full_path = self.frontend_path / file_path
        if not full_path.exists():
            return self.log_test(f"Content Check: {description}", False, f"File not found: {full_path}")
        
        try:
            content = full_path.read_text(encoding='utf-8')
            found = bool(re.search(pattern, content, re.MULTILINE | re.DOTALL))
            self.log_test(f"Content Check: {description}", found, 
                         f"Pattern: {pattern[:50]}..." if len(pattern) > 50 else f"Pattern: {pattern}")
            return found
        except Exception as e:
            return self.log_test(f"Content Check: {description}", False, f"Error reading file: {e}")
    
    def test_import_exists(self, file_path, import_statement, description):
        """Test if a file contains a specific import"""
        pattern = re.escape(import_statement).replace(r'\*', '.*')
        return self.test_file_contains(file_path, pattern, f"Import: {description}")
    
    def run_all_tests(self):
        """Run all frontend implementation tests"""
        print("🧪 Testing Entrepreneur Frontend Implementation")
        print("=" * 60)
        
        # Test 1: Check if new entrepreneur business idea detail page exists
        self.test_file_exists(
            "pages/entrepreneur/EntrepreneurBusinessIdeaDetailPage.tsx",
            "Entrepreneur Business Idea Detail Page"
        )
        
        # Test 2: Check if entrepreneur business ideas page exists
        self.test_file_exists(
            "pages/entrepreneur/EntrepreneurBusinessIdeasPage.tsx",
            "Entrepreneur Business Ideas Page"
        )
        
        # Test 3: Check if component registry is updated
        self.test_file_contains(
            "config/componentRegistry.ts",
            r"EntrepreneurBusinessIdeaDetailPage",
            "Component Registry includes new detail page"
        )
        
        # Test 4: Check if routes are added
        self.test_file_contains(
            "routes/ConsolidatedAppRoutes.tsx",
            r"/:role/business-ideas/:id",
            "Route for business idea detail page"
        )
        
        # Test 5: Check if API method exists
        self.test_file_contains(
            "services/incubatorApi.ts",
            r"getBusinessIdeaById",
            "API method for getting business idea by ID"
        )
        
        # Test 6: Check proper imports in detail page
        self.test_import_exists(
            "pages/entrepreneur/EntrepreneurBusinessIdeaDetailPage.tsx",
            "import { BusinessIdea, businessIdeasAPI, progressUpdatesAPI } from '../../services/incubatorApi';",
            "API imports in detail page"
        )
        
        # Test 7: Check if BusinessIdeaForm is properly imported
        self.test_import_exists(
            "pages/entrepreneur/EntrepreneurBusinessIdeaDetailPage.tsx",
            "import BusinessIdeaForm from '../../components/incubator/forms/BusinessIdeaForm';",
            "BusinessIdeaForm import in detail page"
        )
        
        # Test 8: Check if component registry has special handling for entrepreneurs
        self.test_file_contains(
            "config/componentRegistry.ts",
            r"business-ideas.*entrepreneur",
            "Special entrepreneur handling in component registry"
        )
        
        # Test 9: Check if API endpoints are consistent
        self.test_file_contains(
            "services/incubatorApi.ts",
            r"/api/incubator/business-ideas/",
            "Consistent API endpoint paths"
        )
        
        # Test 10: Check if proper props are used in forms
        self.test_file_contains(
            "pages/entrepreneur/EntrepreneurBusinessIdeasPage.tsx",
            r"isSubmitting.*mode",
            "Correct props for BusinessIdeaForm"
        )
        
        # Test 11: Check if RTL support is implemented
        self.test_file_contains(
            "pages/entrepreneur/EntrepreneurBusinessIdeaDetailPage.tsx",
            r"isRTL.*flex-row-reverse",
            "RTL support in detail page"
        )
        
        # Test 12: Check if proper styling is used
        self.test_file_contains(
            "pages/entrepreneur/EntrepreneurBusinessIdeaDetailPage.tsx",
            r"bg-white/10.*backdrop-blur-sm",
            "Entrepreneur glassmorphism styling"
        )
        
        # Test 13: Check if navigation is properly implemented
        self.test_file_contains(
            "pages/entrepreneur/EntrepreneurBusinessIdeaDetailPage.tsx",
            r"navigate.*business-ideas",
            "Navigation back to business ideas list"
        )
        
        # Test 14: Check if CRUD operations are implemented
        self.test_file_contains(
            "pages/entrepreneur/EntrepreneurBusinessIdeaDetailPage.tsx",
            r"handleUpdate.*handleDelete",
            "CRUD operations in detail page"
        )
        
        # Test 15: Check if error handling is implemented
        self.test_file_contains(
            "pages/entrepreneur/EntrepreneurBusinessIdeaDetailPage.tsx",
            r"error.*loading",
            "Error and loading state handling"
        )
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   - {result['test']}: {result['details']}")
        
        return failed_tests == 0

if __name__ == "__main__":
    tester = EntrepreneurFrontendTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed! The entrepreneur business ideas fix is properly implemented.")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
    
    exit(0 if success else 1)
