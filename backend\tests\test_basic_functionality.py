"""
Basic functionality tests for Yasmeen AI Platform
"""

from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from rest_framework.test import APITestCase
from rest_framework import status
import json


class BasicAPITestCase(APITestCase):
    """Test basic API functionality"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_api_root_requires_authentication(self):
        """Test that API root requires authentication"""
        response = self.client.get('/api/')
        self.assertEqual(response.status_code, 401)
        
        data = json.loads(response.content)
        self.assertIn('detail', data)
        self.assertIn('Authentication credentials were not provided', data['detail'])
    
    def test_admin_page_loads(self):
        """Test that Django admin page loads"""
        response = self.client.get('/admin/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Django administration')
    
    def test_cors_headers_present(self):
        """Test that CORS headers are present"""
        response = self.client.get('/api/')
        self.assertIn('Vary', response)
    
    def test_security_headers_present(self):
        """Test that security headers are present"""
        response = self.client.get('/admin/')
        self.assertEqual(response['X-Frame-Options'], 'DENY')
        self.assertEqual(response['X-Content-Type-Options'], 'nosniff')
        self.assertEqual(response['Referrer-Policy'], 'same-origin')


class UserModelTestCase(TestCase):
    """Test user-related functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_user_creation(self):
        """Test that users can be created"""
        self.assertEqual(self.user.username, 'testuser')
        self.assertEqual(self.user.email, '<EMAIL>')
        self.assertTrue(self.user.check_password('testpass123'))
    
    def test_user_string_representation(self):
        """Test user string representation"""
        self.assertEqual(str(self.user), 'testuser')


class URLPatternsTestCase(TestCase):
    """Test URL patterns and routing"""
    
    def test_admin_url_resolves(self):
        """Test that admin URL resolves"""
        response = self.client.get('/admin/')
        self.assertEqual(response.status_code, 200)
    
    def test_api_url_resolves(self):
        """Test that API URL resolves"""
        response = self.client.get('/api/')
        # Should return 401 (unauthorized) not 404 (not found)
        self.assertEqual(response.status_code, 401)
    
    def test_static_files_url_resolves(self):
        """Test that static files URL resolves"""
        response = self.client.get('/static/admin/css/base.css')
        self.assertEqual(response.status_code, 200)


class DatabaseTestCase(TestCase):
    """Test database connectivity and basic operations"""
    
    def test_database_connection(self):
        """Test that database connection works"""
        # Create a user to test database write
        user = User.objects.create_user(
            username='dbtest',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Test database read
        retrieved_user = User.objects.get(username='dbtest')
        self.assertEqual(user.id, retrieved_user.id)
        self.assertEqual(user.username, retrieved_user.username)
    
    def test_database_transactions(self):
        """Test database transactions work correctly"""
        initial_count = User.objects.count()
        
        # Create multiple users
        User.objects.create_user(username='user1', password='pass1')
        User.objects.create_user(username='user2', password='pass2')
        
        final_count = User.objects.count()
        self.assertEqual(final_count, initial_count + 2)


class HealthCheckTestCase(TestCase):
    """Test application health and basic functionality"""
    
    def test_django_settings_loaded(self):
        """Test that Django settings are loaded correctly"""
        from django.conf import settings
        self.assertTrue(settings.configured)
        self.assertIsNotNone(settings.SECRET_KEY)
    
    def test_installed_apps_loaded(self):
        """Test that all required apps are installed"""
        from django.conf import settings
        
        required_apps = [
            'django.contrib.admin',
            'django.contrib.auth',
            'rest_framework',
            'corsheaders',
            'core',
            'api',
            'users',
        ]
        
        for app in required_apps:
            self.assertIn(app, settings.INSTALLED_APPS)
    
    def test_middleware_configured(self):
        """Test that required middleware is configured"""
        from django.conf import settings
        
        required_middleware = [
            'corsheaders.middleware.CorsMiddleware',
            'django.middleware.security.SecurityMiddleware',
            'django.contrib.sessions.middleware.SessionMiddleware',
            'django.middleware.common.CommonMiddleware',
        ]
        
        for middleware in required_middleware:
            self.assertIn(middleware, settings.MIDDLEWARE)
