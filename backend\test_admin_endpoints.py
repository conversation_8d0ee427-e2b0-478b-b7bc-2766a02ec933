#!/usr/bin/env python
"""
Simple test script for admin endpoints
"""
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import Client

def test_admin_endpoints():
    print('🔍 TESTING ADMIN DASHBOARD ENDPOINTS')
    print('=' * 50)

    try:
        # Create test client
        client = Client()

        # Create a superuser for testing
        admin_user, created = User.objects.get_or_create(
            username='testadmin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True
            }
        )
        admin_user.set_password('testpass123')
        admin_user.save()

        # Login as admin
        login_response = client.login(username='testadmin', password='testpass123')
        print(f'Admin Login: {login_response}')

        if not login_response:
            print('❌ FAILED! Could not login as admin')
            return

        # Test dashboard stats endpoint
        print('\n1. Testing Dashboard Stats Endpoint...')
        response = client.get('/api/admin/dashboard/stats/')
        print(f'   Status: {response.status_code}')

        if response.status_code == 200:
            data = response.json()
            print('   ✅ SUCCESS! Dashboard stats endpoint working')
            print(f'   Users: {data.get("users", {}).get("total", 0)} total')
            print(f'   Events: {data.get("events", {}).get("total_events", 0)} total')
        else:
            print(f'   ❌ FAILED! Status: {response.status_code}')

        # Test users endpoint
        print('\n2. Testing Admin Users Endpoint...')
        response = client.get('/api/admin/users/')
        print(f'   Status: {response.status_code}')

        if response.status_code == 200:
            data = response.json()
            print('   ✅ SUCCESS! Admin users endpoint working')
            print(f'   Users count: {data.get("count", 0)}')
        else:
            print(f'   ❌ FAILED! Status: {response.status_code}')

        # Test system health endpoint
        print('\n3. Testing System Health Endpoint...')
        response = client.get('/api/admin/system/')
        print(f'   Status: {response.status_code}')

        if response.status_code == 200:
            print('   ✅ SUCCESS! System health endpoint working')
        else:
            print(f'   ❌ FAILED! Status: {response.status_code}')

        # Test activity logs endpoint
        print('\n4. Testing Activity Logs Endpoint...')
        response = client.get('/api/admin/logs/')
        print(f'   Status: {response.status_code}')

        if response.status_code == 200:
            print('   ✅ SUCCESS! Activity logs endpoint working')
        else:
            print(f'   ❌ FAILED! Status: {response.status_code}')

        print('\n' + '=' * 50)
        print('✅ ADMIN ENDPOINT TESTING COMPLETE!')

    except Exception as e:
        print(f'❌ ERROR: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_admin_endpoints()
