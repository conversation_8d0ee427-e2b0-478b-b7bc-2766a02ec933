#!/usr/bin/env python3
"""
Comprehensive End-to-End Test for Entrepreneur APIs
Tests all entrepreneur-related endpoints and functionality
"""

import requests
import json
import sys
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

class EntrepreneurAPITester:
    def __init__(self):
        self.session = requests.Session()
        self.auth_token = None
        self.user_id = None
        self.test_results = []
        
    def log_test(self, test_name, success, details=""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")
        self.test_results.append({
            'test': test_name,
            'success': success,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
    
    def test_server_health(self):
        """Test if backend server is running"""
        try:
            response = self.session.get(f"{BASE_URL}/admin/", timeout=5)
            self.log_test("Backend Server Health", response.status_code in [200, 302], 
                         f"Status: {response.status_code}")
            return response.status_code in [200, 302]
        except Exception as e:
            self.log_test("Backend Server Health", False, f"Error: {str(e)}")
            return False
    
    def test_user_registration(self):
        """Test user registration for entrepreneur"""
        try:
            # Try different registration endpoints
            timestamp = int(time.time())
            user_data = {
                "username": f"test_entrepreneur_{timestamp}",
                "email": f"entrepreneur_{timestamp}@test.com",
                "password": "TestPass123!",
                "first_name": "Test",
                "last_name": "Entrepreneur",
                "role": "entrepreneur"
            }

            # Try the correct registration endpoint
            endpoint = f"{API_BASE}/auth/register-enhanced/"

            response = self.session.post(endpoint, json=user_data)
            success = response.status_code in [200, 201]

            if success:
                data = response.json()
                self.user_id = data.get('user', {}).get('id') or data.get('id')
                self.log_test("User Registration", True,
                             f"Status: {response.status_code}, User ID: {self.user_id}")
            else:
                self.log_test("User Registration", False,
                             f"Status: {response.status_code}, Response: {response.text[:200]}")
            return success

        except Exception as e:
            self.log_test("User Registration", False, f"Error: {str(e)}")
            return False
    
    def test_user_login(self):
        """Test user login"""
        try:
            # Use a test user that should exist or create one
            login_data = {
                "username": "admin",  # Try with admin user first
                "password": "admin123"
            }

            response = self.session.post(f"{API_BASE}/auth/login/", json=login_data)
            success = response.status_code == 200

            if success:
                data = response.json()
                self.auth_token = data.get('access') or data.get('access_token') or data.get('token')
                if self.auth_token:
                    self.session.headers.update({
                        'Authorization': f'Bearer {self.auth_token}'
                    })

            self.log_test("User Login", success,
                         f"Status: {response.status_code}, Token: {'Yes' if self.auth_token else 'No'}")

            # If admin login fails, try token endpoint
            if not success:
                token_response = self.session.post(f"{API_BASE}/auth/token/", json=login_data)
                if token_response.status_code == 200:
                    token_data = token_response.json()
                    self.auth_token = token_data.get('access')
                    if self.auth_token:
                        self.session.headers.update({
                            'Authorization': f'Bearer {self.auth_token}'
                        })
                        self.log_test("User Login (Token)", True, "Got token via /token/ endpoint")
                        return True

            return success

        except Exception as e:
            self.log_test("User Login", False, f"Error: {str(e)}")
            return False
    
    def test_business_ideas_api(self):
        """Test Business Ideas CRUD operations"""
        try:
            # Test GET business ideas
            response = self.session.get(f"{API_BASE}/incubator/business-ideas/")
            get_success = response.status_code == 200
            self.log_test("Business Ideas - GET", get_success, 
                         f"Status: {response.status_code}")
            
            # Test POST business idea
            idea_data = {
                "title": "Test Business Idea",
                "description": "A test business idea for API testing",
                "problem_statement": "Testing API endpoints",
                "solution_description": "Automated testing solution",
                "target_audience": "Developers",
                "current_stage": "concept"
            }
            
            response = self.session.post(f"{API_BASE}/incubator/business-ideas/", json=idea_data)
            post_success = response.status_code in [200, 201]
            idea_id = None
            
            if post_success:
                data = response.json()
                idea_id = data.get('id')
                
            self.log_test("Business Ideas - POST", post_success, 
                         f"Status: {response.status_code}, ID: {idea_id}")
            
            return get_success and post_success
            
        except Exception as e:
            self.log_test("Business Ideas API", False, f"Error: {str(e)}")
            return False
    
    def test_business_plans_api(self):
        """Test Business Plans CRUD operations"""
        try:
            # Test GET business plans
            response = self.session.get(f"{API_BASE}/business-plans/")
            get_success = response.status_code == 200
            self.log_test("Business Plans - GET", get_success, 
                         f"Status: {response.status_code}")
            
            # Test POST business plan
            plan_data = {
                "title": "Test Business Plan",
                "description": "A test business plan for API testing",
                "status": "draft"
            }
            
            response = self.session.post(f"{API_BASE}/business-plans/", json=plan_data)
            post_success = response.status_code in [200, 201]
            plan_id = None
            
            if post_success:
                data = response.json()
                plan_id = data.get('id')
                
            self.log_test("Business Plans - POST", post_success, 
                         f"Status: {response.status_code}, ID: {plan_id}")
            
            return get_success and post_success
            
        except Exception as e:
            self.log_test("Business Plans API", False, f"Error: {str(e)}")
            return False
    
    def test_dashboard_analytics(self):
        """Test Dashboard Analytics API"""
        try:
            response = self.session.get(f"{API_BASE}/analytics/dashboard-overview/")
            success = response.status_code == 200
            
            if success:
                data = response.json()
                has_data = bool(data)
                
            self.log_test("Dashboard Analytics", success, 
                         f"Status: {response.status_code}, Has Data: {has_data if success else 'N/A'}")
            return success
            
        except Exception as e:
            self.log_test("Dashboard Analytics", False, f"Error: {str(e)}")
            return False
    
    def test_incubator_resources(self):
        """Test Incubator Resources API"""
        try:
            response = self.session.get(f"{API_BASE}/incubator/resources/")
            success = response.status_code == 200

            self.log_test("Incubator Resources", success,
                         f"Status: {response.status_code}")
            return success

        except Exception as e:
            self.log_test("Incubator Resources", False, f"Error: {str(e)}")
            return False

    def test_public_endpoints(self):
        """Test public endpoints that don't require authentication"""
        try:
            # Remove auth header temporarily
            auth_header = self.session.headers.pop('Authorization', None)

            # Test various public endpoints
            endpoints = [
                f"{BASE_URL}/",
                f"{BASE_URL}/admin/",
                f"{API_BASE}/",
                f"{API_BASE}/health/",
                f"{BASE_URL}/api/health/"
            ]

            success_count = 0
            for endpoint in endpoints:
                try:
                    response = self.session.get(endpoint, timeout=5)
                    if response.status_code in [200, 302, 404]:  # 404 is ok for non-existent endpoints
                        success_count += 1
                        self.log_test(f"Public Endpoint {endpoint}", True,
                                     f"Status: {response.status_code}")
                except Exception as e:
                    self.log_test(f"Public Endpoint {endpoint}", False, f"Error: {str(e)}")

            # Restore auth header if it existed
            if auth_header:
                self.session.headers['Authorization'] = auth_header

            return success_count > 0

        except Exception as e:
            self.log_test("Public Endpoints", False, f"Error: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all entrepreneur API tests"""
        print("🚀 Starting Comprehensive Entrepreneur API Tests")
        print("=" * 60)
        
        # Test server health first
        if not self.test_server_health():
            print("❌ Backend server is not running. Please start the Django server.")
            return False
        
        # Run authentication tests
        print("\n🔐 Testing Authentication...")
        self.test_user_registration()
        self.test_user_login()
        
        # Test core entrepreneur APIs
        print("\n📊 Testing Core APIs...")
        self.test_business_ideas_api()
        self.test_business_plans_api()
        self.test_dashboard_analytics()
        self.test_incubator_resources()

        # Test without authentication for public endpoints
        print("\n🌐 Testing Public APIs...")
        self.test_public_endpoints()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   - {result['test']}: {result['details']}")
        
        return failed_tests == 0

if __name__ == "__main__":
    tester = EntrepreneurAPITester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
