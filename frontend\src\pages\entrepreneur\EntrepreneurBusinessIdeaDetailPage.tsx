/**
 * Entrepreneur Business Idea Detail Page
 * Dedicated page for entrepreneurs to view and manage individual business ideas
 * Matches the entrepreneur dashboard styling and functionality
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import { RTLText, RTLFlex } from '../../components/common';
import { BusinessIdea, businessIdeasAPI, progressUpdatesAPI } from '../../services/incubatorApi';
import BusinessIdeaForm from '../../components/incubator/forms/BusinessIdeaForm';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Share2,
  Plus,
  Calendar,
  User,
  Target,
  TrendingUp,
  Lightbulb,
  CheckCircle,
  Clock,
  AlertCircle,
  MoreHorizontal,
  BookOpen,
  Users,
  DollarSign,
  RefreshCw
} from 'lucide-react';

const EntrepreneurBusinessIdeaDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  // State management
  const [businessIdea, setBusinessIdea] = useState<BusinessIdea | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [progressUpdates, setProgressUpdates] = useState<any[]>([]);

  // Load business idea data
  useEffect(() => {
    if (id) {
      loadBusinessIdea();
    }
  }, [id]);

  const loadBusinessIdea = async () => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      // Load business idea details
      const idea = await businessIdeasAPI.getBusinessIdeaById(parseInt(id));
      setBusinessIdea(idea);

      // Load progress updates if available
      try {
        const updates = await progressUpdatesAPI.getProgressUpdates();
        const ideaUpdates = updates.filter(update => update.business_idea === parseInt(id));
        setProgressUpdates(ideaUpdates);
      } catch (updateError) {
        console.warn('Could not load progress updates:', updateError);
        setProgressUpdates([]);
      }
    } catch (err) {
      console.error('Error loading business idea:', err);
      setError(t('entrepreneur.businessIdeas.loadError', 'Failed to load business idea'));
    } finally {
      setLoading(false);
    }
  };

  // Handle update
  const handleUpdate = async (data: Partial<BusinessIdea>) => {
    if (!businessIdea) return false;

    try {
      const updatedIdea = await businessIdeasAPI.updateBusinessIdea(businessIdea.id, data);
      setBusinessIdea(updatedIdea);
      setShowEditForm(false);
      return true;
    } catch (err) {
      console.error('Error updating business idea:', err);
      return false;
    }
  };

  // Handle delete
  const handleDelete = async () => {
    if (!businessIdea) return;

    try {
      await businessIdeasAPI.deleteBusinessIdea(businessIdea.id);
      navigate('/entrepreneur/business-ideas');
    } catch (err) {
      console.error('Error deleting business idea:', err);
    }
  };

  // Status color helper
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'concept': return 'text-blue-400 bg-blue-400/20';
      case 'validation': return 'text-yellow-400 bg-yellow-400/20';
      case 'development': return 'text-orange-400 bg-orange-400/20';
      case 'scaling': return 'text-green-400 bg-green-400/20';
      case 'established': return 'text-purple-400 bg-purple-400/20';
      default: return 'text-gray-400 bg-gray-400/20';
    }
  };

  // Status icon helper
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'concept': return <Lightbulb className="w-5 h-5" />;
      case 'validation': return <TrendingUp className="w-5 h-5" />;
      case 'development': return <Users className="w-5 h-5" />;
      case 'scaling': return <DollarSign className="w-5 h-5" />;
      case 'established': return <CheckCircle className="w-5 h-5" />;
      default: return <Clock className="w-5 h-5" />;
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin text-purple-400 mx-auto mb-4" />
          <p className="text-gray-400">{t('common.loading', 'Loading...')}</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !businessIdea) {
    return (
      <div className="space-y-6">
        <div className={`flex items-center space-x-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          <button
            onClick={() => navigate('/entrepreneur/business-ideas')}
            className="p-2 bg-white/10 text-white rounded-lg hover:bg-white/20 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <RTLText as="h1" className="text-2xl font-bold text-white">
            {t('entrepreneur.businessIdeas.notFound', 'Business Idea Not Found')}
          </RTLText>
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-12 border border-white/20 text-center">
          <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">
            {error || t('entrepreneur.businessIdeas.notFound', 'Business Idea Not Found')}
          </h3>
          <p className="text-gray-400 mb-6">
            {t('entrepreneur.businessIdeas.notFoundDesc', 'The business idea you are looking for does not exist or you do not have permission to view it.')}
          </p>
          <button
            onClick={() => navigate('/entrepreneur/business-ideas')}
            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-200"
          >
            {t('entrepreneur.businessIdeas.backToList', 'Back to Business Ideas')}
          </button>
        </div>
      </div>
    );
  }

  // Check if user owns this idea
  const isOwner = businessIdea.owner.id === user?.id;

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center space-x-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
            <button
              onClick={() => navigate('/entrepreneur/business-ideas')}
              className="p-2 bg-white/10 text-white rounded-lg hover:bg-white/20 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <RTLText as="h1" className="text-3xl font-bold text-white">
                {businessIdea.title}
              </RTLText>
              <div className={`flex items-center space-x-4 mt-2 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                <span className={`text-sm px-3 py-1 rounded-full ${getStatusColor(businessIdea.current_stage)}`}>
                  {businessIdea.current_stage}
                </span>
                <span className="text-gray-400 text-sm">
                  {t('entrepreneur.businessIdeas.createdOn', 'Created on')} {new Date(businessIdea.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>

          {isOwner && (
            <div className={`flex items-center space-x-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
              <button
                onClick={() => setShowEditForm(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600/20 text-blue-400 rounded-lg hover:bg-blue-600/30 transition-colors"
              >
                <Edit className="w-4 h-4" />
                <span>{t('common.edit', 'Edit')}</span>
              </button>
              <button
                onClick={() => navigate(`/entrepreneur/business-plans/new?ideaId=${businessIdea.id}`)}
                className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-200"
              >
                <BookOpen className="w-4 h-4" />
                <span>{t('entrepreneur.businessIdeas.createPlan', 'Create Business Plan')}</span>
              </button>
              <div className="relative group">
                <button className="p-2 bg-white/10 text-white rounded-lg hover:bg-white/20 transition-colors">
                  <MoreHorizontal className="w-4 h-4" />
                </button>
                <div className="absolute right-0 top-full mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                  <div className="py-2">
                    <button
                      onClick={() => setShowDeleteDialog(true)}
                      className="w-full px-4 py-2 text-left text-red-400 hover:bg-red-900/20 hover:text-red-300 transition-colors flex items-center space-x-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      <span>{t('common.delete', 'Delete')}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Business Idea Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Description */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h3 className="text-lg font-semibold text-white mb-4">
                {t('entrepreneur.businessIdeas.description', 'Description')}
              </h3>
              <p className="text-gray-300 leading-relaxed">{businessIdea.description}</p>
            </div>

            {/* Problem Statement */}
            {businessIdea.problem_statement && (
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {t('entrepreneur.businessIdeas.problemStatement', 'Problem Statement')}
                </h3>
                <p className="text-gray-300 leading-relaxed">{businessIdea.problem_statement}</p>
              </div>
            )}

            {/* Solution */}
            {businessIdea.solution_description && (
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {t('entrepreneur.businessIdeas.solution', 'Solution')}
                </h3>
                <p className="text-gray-300 leading-relaxed">{businessIdea.solution_description}</p>
              </div>
            )}

            {/* Target Audience */}
            {businessIdea.target_audience && (
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {t('entrepreneur.businessIdeas.targetAudience', 'Target Audience')}
                </h3>
                <p className="text-gray-300 leading-relaxed">{businessIdea.target_audience}</p>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status & Metrics */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h3 className="text-lg font-semibold text-white mb-4">
                {t('entrepreneur.businessIdeas.overview', 'Overview')}
              </h3>
              <div className="space-y-4">
                <div className={`flex items-center space-x-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  {getStatusIcon(businessIdea.current_stage)}
                  <div>
                    <p className="text-sm text-gray-400">{t('entrepreneur.businessIdeas.currentStage', 'Current Stage')}</p>
                    <p className="text-white font-medium">{businessIdea.current_stage}</p>
                  </div>
                </div>

                <div className={`flex items-center space-x-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  <User className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-400">{t('entrepreneur.businessIdeas.owner', 'Owner')}</p>
                    <p className="text-white font-medium">{businessIdea.owner.first_name} {businessIdea.owner.last_name}</p>
                  </div>
                </div>

                <div className={`flex items-center space-x-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  <Calendar className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-400">{t('entrepreneur.businessIdeas.lastUpdated', 'Last Updated')}</p>
                    <p className="text-white font-medium">{new Date(businessIdea.updated_at).toLocaleDateString()}</p>
                  </div>
                </div>

                {businessIdea.progress_count && (
                  <div className={`flex items-center space-x-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                    <TrendingUp className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-400">{t('entrepreneur.businessIdeas.progressUpdates', 'Progress Updates')}</p>
                      <p className="text-white font-medium">{businessIdea.progress_count}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Quick Actions */}
            {isOwner && (
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {t('entrepreneur.businessIdeas.quickActions', 'Quick Actions')}
                </h3>
                <div className="space-y-3">
                  <button
                    onClick={() => navigate(`/entrepreneur/business-plans/new?ideaId=${businessIdea.id}`)}
                    className="w-full flex items-center space-x-3 p-3 bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-lg hover:from-purple-600/30 hover:to-blue-600/30 transition-all duration-200 text-left"
                  >
                    <BookOpen className="w-5 h-5 text-purple-400" />
                    <span className="text-white">{t('entrepreneur.businessIdeas.createPlan', 'Create Business Plan')}</span>
                  </button>
                  <button
                    onClick={() => setShowEditForm(true)}
                    className="w-full flex items-center space-x-3 p-3 bg-blue-600/20 rounded-lg hover:bg-blue-600/30 transition-all duration-200 text-left"
                  >
                    <Edit className="w-5 h-5 text-blue-400" />
                    <span className="text-white">{t('common.edit', 'Edit Idea')}</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Edit Form Modal */}
      {showEditForm && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto border border-white/20">
            <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h2 className="text-xl font-semibold text-white">
                {t('entrepreneur.businessIdeas.editIdea', 'Edit Business Idea')}
              </h2>
              <button
                onClick={() => setShowEditForm(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                ×
              </button>
            </div>
            <BusinessIdeaForm
              initialData={businessIdea}
              onSubmit={handleUpdate}
              onCancel={() => setShowEditForm(false)}
              isLoading={loading}
              mode="edit"
            />
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-xl p-6 w-full max-w-md border border-white/20">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">
                {t('entrepreneur.businessIdeas.confirmDelete', 'Delete Business Idea')}
              </h3>
              <p className="text-gray-400 mb-6">
                {t('entrepreneur.businessIdeas.deleteWarning', 'Are you sure you want to delete this business idea? This action cannot be undone.')}
              </p>
              <div className={`flex items-center space-x-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
                <button
                  onClick={() => setShowDeleteDialog(false)}
                  className="flex-1 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={handleDelete}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  {t('common.delete', 'Delete')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default EntrepreneurBusinessIdeaDetailPage;
