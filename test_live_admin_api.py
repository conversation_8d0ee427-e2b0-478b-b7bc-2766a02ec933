#!/usr/bin/env python
"""
End-to-End Live API Testing Script
Tests the actual running servers with HTTP requests
"""
import requests
import json
import time

def test_live_admin_api():
    print('🚀 LIVE END-TO-END ADMIN API TESTING')
    print('=' * 60)
    
    # Server URLs
    backend_url = 'http://localhost:8000'
    frontend_url = 'http://localhost:5173'  # Vite default port
    
    # Test session for maintaining cookies
    session = requests.Session()
    
    try:
        # Step 1: Test Backend Server Health
        print('\n1. Testing Backend Server Health...')
        try:
            response = session.get(f'{backend_url}/api/')
            print(f'   Backend Status: {response.status_code} ✅')
        except requests.exceptions.ConnectionError:
            print('   ❌ Backend server not running on port 8000')
            return
        
        # Step 2: Test Frontend Server Health  
        print('\n2. Testing Frontend Server Health...')
        try:
            response = session.get(frontend_url)
            print(f'   Frontend Status: {response.status_code} ✅')
        except requests.exceptions.ConnectionError:
            print('   ❌ Frontend server not running on port 5173')
            # Try alternative ports
            for port in [3000, 3002, 4178]:
                try:
                    response = session.get(f'http://localhost:{port}')
                    print(f'   Frontend found on port {port}: {response.status_code} ✅')
                    frontend_url = f'http://localhost:{port}'
                    break
                except:
                    continue
            else:
                print('   ❌ Frontend server not found on common ports')
        
        # Step 3: Create Admin User and Login
        print('\n3. Creating Admin User and Testing Login...')
        
        # Get CSRF token first
        csrf_response = session.get(f'{backend_url}/api/auth/login/')
        csrf_token = None
        if 'csrftoken' in session.cookies:
            csrf_token = session.cookies['csrftoken']
        
        # Try to login (this will create session)
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrf_token if csrf_token else ''
        }
        
        login_response = session.post(
            f'{backend_url}/api/auth/login/',
            json=login_data,
            headers=headers
        )
        print(f'   Login Status: {login_response.status_code}')
        
        # Step 4: Test Admin Dashboard Stats Endpoint
        print('\n4. Testing Admin Dashboard Stats...')
        stats_response = session.get(f'{backend_url}/api/admin/dashboard/stats/')
        print(f'   Dashboard Stats Status: {stats_response.status_code}')
        
        if stats_response.status_code == 200:
            data = stats_response.json()
            print('   ✅ SUCCESS! Dashboard stats working')
            print(f'   Users: {data.get("users", {}).get("total", 0)} total')
            print(f'   Events: {data.get("events", {}).get("total_events", 0)} total')
            print(f'   Resources: {data.get("resources", {}).get("total_resources", 0)} total')
            print(f'   Posts: {data.get("posts", {}).get("total_posts", 0)} total')
        elif stats_response.status_code == 403:
            print('   ⚠️  Permission denied - need admin login')
        else:
            print(f'   ❌ FAILED! Status: {stats_response.status_code}')
            print(f'   Response: {stats_response.text[:200]}...')
        
        # Step 5: Test Admin Users Endpoint
        print('\n5. Testing Admin Users Endpoint...')
        users_response = session.get(f'{backend_url}/api/admin/users/')
        print(f'   Admin Users Status: {users_response.status_code}')
        
        if users_response.status_code == 200:
            data = users_response.json()
            print('   ✅ SUCCESS! Admin users endpoint working')
            print(f'   Users count: {data.get("count", 0)}')
        elif users_response.status_code == 403:
            print('   ⚠️  Permission denied - need admin login')
        else:
            print(f'   ❌ FAILED! Status: {users_response.status_code}')
        
        # Step 6: Test System Health Endpoint
        print('\n6. Testing System Health Endpoint...')
        health_response = session.get(f'{backend_url}/api/admin/system/')
        print(f'   System Health Status: {health_response.status_code}')
        
        if health_response.status_code == 200:
            print('   ✅ SUCCESS! System health endpoint working')
        elif health_response.status_code == 403:
            print('   ⚠️  Permission denied - need admin login')
        else:
            print(f'   ❌ FAILED! Status: {health_response.status_code}')
        
        # Step 7: Test Activity Logs Endpoint
        print('\n7. Testing Activity Logs Endpoint...')
        logs_response = session.get(f'{backend_url}/api/admin/logs/')
        print(f'   Activity Logs Status: {logs_response.status_code}')
        
        if logs_response.status_code == 200:
            print('   ✅ SUCCESS! Activity logs endpoint working')
        elif logs_response.status_code == 403:
            print('   ⚠️  Permission denied - need admin login')
        else:
            print(f'   ❌ FAILED! Status: {logs_response.status_code}')
        
        # Step 8: Test Other API Endpoints
        print('\n8. Testing Other API Endpoints...')
        
        # Test user approvals
        approvals_response = session.get(f'{backend_url}/api/users/approvals/')
        print(f'   User Approvals Status: {approvals_response.status_code}')
        
        # Test membership applications
        apps_response = session.get(f'{backend_url}/api/membership-applications/')
        print(f'   Membership Applications Status: {apps_response.status_code}')
        
        print('\n' + '=' * 60)
        print('🎉 END-TO-END API TESTING COMPLETE!')
        print('\n📊 SUMMARY:')
        print(f'   Backend Server: ✅ Running on {backend_url}')
        print(f'   Frontend Server: ✅ Running on {frontend_url}')
        print('   Admin API Endpoints: Ready for testing')
        print('\n🌐 Ready to open browser!')
        
        return True
        
    except Exception as e:
        print(f'\n❌ ERROR: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_live_admin_api()
