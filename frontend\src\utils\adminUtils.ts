/**
 * 🔧 ADMIN UTILITIES CONSOLIDATION
 * Centralized utilities for all admin pages to eliminate duplication
 * 
 * This file consolidates:
 * - Data loading functions
 * - Refresh handlers
 * - Status helpers
 * - Formatting utilities
 * - Common admin patterns
 */

import { adminAPI } from '../services/api';

// ========================================
// TYPES & INTERFACES
// ========================================

export interface AdminDataLoadOptions {
  showLoading?: boolean;
  showError?: boolean;
  timeout?: number;
}

export interface AdminRefreshOptions {
  silent?: boolean;
  force?: boolean;
}

export interface StatusInfo {
  status: 'healthy' | 'warning' | 'error' | 'unknown';
  color: string;
  icon: string;
  text: string;
}

// ========================================
// DATA LOADING UTILITIES
// ========================================

/**
 * Generic admin data loader with consistent error handling
 */
export const loadAdminData = async <T>(
  apiCall: () => Promise<T>,
  options: AdminDataLoadOptions = {}
): Promise<{ data: T | null; error: string | null }> => {
  const { timeout = 30000 } = options;

  try {
    const timeoutPromise = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error('Request timeout')), timeout)
    );

    const data = await Promise.race([apiCall(), timeoutPromise]);
    return { data, error: null };
  } catch (error) {
    console.error('Admin data loading error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to load data';
    return { data: null, error: errorMessage };
  }
};

/**
 * Load dashboard stats with consistent structure
 */
export const loadDashboardStats = async () => {
  return loadAdminData(() => adminAPI.getAllStats());
};

/**
 * Load system health data
 */
export const loadSystemHealth = async () => {
  return loadAdminData(() => adminAPI.getSystemHealth());
};

/**
 * Load activity logs
 */
export const loadActivityLogs = async (limit: number = 10) => {
  return loadAdminData(() => adminAPI.getActivityLogs(limit));
};

/**
 * Load all admin data in parallel
 */
export const loadAllAdminData = async () => {
  console.log('🔄 loadAllAdminData: Starting to load admin data...');

  const [dashboardStats, systemHealth, activityLogs] = await Promise.all([
    loadDashboardStats(),
    loadSystemHealth(),
    loadActivityLogs(10)
  ]);

  console.log('📊 loadAllAdminData: Raw results:', {
    dashboardStats: dashboardStats.data ? 'loaded' : 'null',
    systemHealth: systemHealth.data ? 'loaded' : 'null',
    activityLogs: activityLogs.data ? 'loaded' : 'null',
    errors: [dashboardStats.error, systemHealth.error, activityLogs.error].filter(Boolean)
  });

  const result = {
    data: {
      dashboardStats: dashboardStats.data,
      systemHealth: systemHealth.data,
      activityLogs: activityLogs.data
    },
    error: [dashboardStats.error, systemHealth.error, activityLogs.error].filter(Boolean)[0] || null
  };

  console.log('✅ loadAllAdminData: Final result structure:', {
    hasData: !!result.data,
    hasError: !!result.error,
    dataKeys: result.data ? Object.keys(result.data) : []
  });

  return result;
};

// ========================================
// REFRESH UTILITIES
// ========================================

/**
 * Generic refresh handler for admin pages
 */
export const createRefreshHandler = (
  loadDataFn: () => Promise<void>,
  setRefreshing: (refreshing: boolean) => void,
  options: AdminRefreshOptions = {}
) => {
  return async () => {
    const { silent = false } = options;
    
    try {
      if (!silent) setRefreshing(true);
      await loadDataFn();
    } catch (error) {
      console.error('Refresh error:', error);
      throw error;
    } finally {
      if (!silent) setRefreshing(false);
    }
  };
};

// ========================================
// STATUS UTILITIES
// ========================================

/**
 * Get status information with consistent styling
 */
export const getStatusInfo = (status: string): StatusInfo => {
  switch (status?.toLowerCase()) {
    case 'healthy':
    case 'running':
    case 'active':
    case 'online':
      return {
        status: 'healthy',
        color: 'text-green-400',
        icon: 'CheckCircle',
        text: 'Healthy'
      };
    
    case 'warning':
    case 'degraded':
    case 'slow':
      return {
        status: 'warning',
        color: 'text-yellow-400',
        icon: 'AlertTriangle',
        text: 'Warning'
      };
    
    case 'error':
    case 'down':
    case 'offline':
    case 'failed':
      return {
        status: 'error',
        color: 'text-red-400',
        icon: 'XCircle',
        text: 'Error'
      };
    
    default:
      return {
        status: 'unknown',
        color: 'text-gray-400',
        icon: 'HelpCircle',
        text: 'Unknown'
      };
  }
};

/**
 * Get progress bar color based on percentage
 */
export const getProgressColor = (percentage: number): string => {
  if (percentage >= 80) return 'from-red-500 to-red-600';
  if (percentage >= 60) return 'from-yellow-500 to-yellow-600';
  return 'from-green-500 to-green-600';
};

/**
 * Get HTTP method color
 */
export const getMethodColor = (method: string): string => {
  switch (method?.toUpperCase()) {
    case 'GET':
      return 'bg-green-500/20 text-green-300';
    case 'POST':
      return 'bg-blue-500/20 text-blue-300';
    case 'PUT':
      return 'bg-yellow-500/20 text-yellow-300';
    case 'DELETE':
      return 'bg-red-500/20 text-red-300';
    case 'PATCH':
      return 'bg-purple-500/20 text-purple-300';
    default:
      return 'bg-gray-500/20 text-gray-300';
  }
};

/**
 * Get role color
 */
export const getRoleColor = (role: string): string => {
  switch (role?.toLowerCase()) {
    case 'admin':
      return 'bg-purple-500/20 text-purple-400';
    case 'moderator':
      return 'bg-blue-500/20 text-blue-400';
    case 'user':
      return 'bg-green-500/20 text-green-400';
    case 'system':
      return 'bg-gray-500/20 text-gray-400';
    default:
      return 'bg-gray-500/20 text-gray-400';
  }
};

// ========================================
// FORMATTING UTILITIES
// ========================================

/**
 * Format numbers with K/M suffixes
 */
export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
};

/**
 * Format file size
 */
export const formatFileSize = (bytes: number): string => {
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 B';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};

/**
 * Format duration in seconds to human readable
 */
export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  }
  return `${secs}s`;
};

/**
 * Format timestamp for admin displays
 */
export const formatTimestamp = (timestamp: string | Date): string => {
  const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

/**
 * Format percentage with proper rounding
 */
export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${value.toFixed(decimals)}%`;
};

// ========================================
// VALIDATION UTILITIES
// ========================================

/**
 * Validate admin data structure
 */
export const validateAdminData = (data: any, requiredFields: string[]): boolean => {
  if (!data || typeof data !== 'object') return false;
  
  return requiredFields.every(field => {
    const value = field.split('.').reduce((obj, key) => obj?.[key], data);
    return value !== undefined && value !== null;
  });
};

/**
 * Sanitize admin input
 */
export const sanitizeAdminInput = (input: string): string => {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .substring(0, 1000); // Limit length
};

// ========================================
// EXPORT ALL UTILITIES
// ========================================

export default {
  // Data loading
  loadAdminData,
  loadDashboardStats,
  loadSystemHealth,
  loadActivityLogs,
  loadAllAdminData,
  
  // Refresh handling
  createRefreshHandler,
  
  // Status utilities
  getStatusInfo,
  getProgressColor,
  getMethodColor,
  getRoleColor,
  
  // Formatting
  formatNumber,
  formatFileSize,
  formatDuration,
  formatTimestamp,
  formatPercentage,
  
  // Validation
  validateAdminData,
  sanitizeAdminInput
};
