#!/usr/bin/env python
"""
Create admin user for testing
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from users.models import UserProfile, UserRole, UserRoleAssignment

def create_admin_user():
    """Create admin user with proper role assignment"""
    try:
        # Create or get admin user
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True,
                'first_name': 'Admin',
                'last_name': 'User'
            }
        )
        
        # Set password
        admin_user.set_password('admin123')
        admin_user.save()
        
        # Create or get user profile
        profile, profile_created = UserProfile.objects.get_or_create(
            user=admin_user,
            defaults={
                'bio': 'System Administrator',
                'is_active': True,
                'email_notifications': True
            }
        )
        
        # Create or get admin role
        admin_role, role_created = UserRole.objects.get_or_create(
            name='admin',
            defaults={
                'display_name': 'Administrator',
                'description': 'System administrator with full access',
                'permission_level': 100,
                'is_active': True,
                'requires_approval': False
            }
        )
        
        # Assign admin role to user
        role_assignment, assignment_created = UserRoleAssignment.objects.get_or_create(
            user_profile=profile,
            role=admin_role,
            defaults={
                'is_active': True,
                'is_approved': True,
                'notes': 'System admin user'
            }
        )
        
        print(f"✅ Admin user created successfully!")
        print(f"   Username: admin")
        print(f"   Password: admin123")
        print(f"   Email: <EMAIL>")
        print(f"   User created: {created}")
        print(f"   Profile created: {profile_created}")
        print(f"   Role created: {role_created}")
        print(f"   Assignment created: {assignment_created}")
        
        return admin_user
        
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        return None

if __name__ == '__main__':
    create_admin_user()
