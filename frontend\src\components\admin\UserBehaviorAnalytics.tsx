import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>onsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from 'recharts';
import { aiAnalyticsService } from '../../services/aiAnalyticsService';
import <PERSON><PERSON><PERSON><PERSON><PERSON>, { ChartSkeleton, TableSkeleton } from '../common/LoadingSpinner';

// Types
interface UserBehaviorData {
  user_activity: Array<{
    user_id: number;
    username: string;
    total_sessions: number;
    total_messages: number;
    avg_session_duration: number;
    last_active: string;
    preferred_topics: string[];
    engagement_score: number;
  }>;
  session_patterns: Array<{
    hour: number;
    day_of_week: number;
    session_count: number;
    avg_duration: number;
  }>;
  engagement_metrics: {
    daily_active_users: number;
    weekly_active_users: number;
    monthly_active_users: number;
    avg_session_duration: number;
    bounce_rate: number;
    retention_rate: number;
  };
  regional_usage: Array<{
    region: string;
    user_count: number;
    avg_sessions_per_user: number;
  }>;
  summary: {
    total_users: number;
    active_users: number;
    new_users: number;
    returning_users: number;
  };
}

interface UserBehaviorAnalyticsProps {
  days?: number;
}

const UserBehaviorAnalytics: React.FC<UserBehaviorAnalyticsProps> = ({ days = 30 }) => {
  const [selectedDays, setSelectedDays] = useState(days);

  // Fetch user behavior data
  const { data: behaviorData, isLoading, error, refetch } = useQuery({
    queryKey: ['userBehaviorAnalytics', selectedDays],
    queryFn: () => aiAnalyticsService.getUserBehaviorAnalytics(selectedDays),
    refetchInterval: 60000, // Refresh every minute
  });

  // Colors for charts
  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7c7c', '#8dd1e1'];

  // Region name mapping
  const regionNameMap: Record<string, string> = {
    'damascus_dialect': 'دمشق',
    'aleppo_dialect': 'حلب',
    'homs_dialect': 'حمص',
    'latakia_dialect': 'اللاذقية'
  };

  const prepareActivityHeatmapData = () => {
    if (!behaviorData?.session_patterns) return [];

    return behaviorData.session_patterns.map(pattern => ({
      hour: pattern.hour,
      day: days[pattern.day_of_week],
      value: pattern.session_count,
      duration: pattern.avg_duration
    }));
  };

  const prepareEngagementData = () => {
    if (!behaviorData?.user_activity) return [];
    
    return behaviorData.user_activity.map(user => ({
      username: user.username,
      sessions: user.total_sessions,
      messages: user.total_messages,
      engagement: user.engagement_score,
      duration: user.avg_session_duration
    }));
  };

  const prepareRegionalData = () => {
    if (!behaviorData?.regional_usage) return [];
    
    return behaviorData.regional_usage.map(region => ({
      name: regionNameMap[region.region] || region.region,
      users: region.user_count,
      avgSessions: region.avg_sessions_per_user
    }));
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <LoadingSpinner message="جاري تحميل تحليلات سلوك المستخدمين..." />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="bg-white rounded-lg shadow-sm border p-6">
              <ChartSkeleton height={120} />
            </div>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <ChartSkeleton height={300} />
          </div>
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <ChartSkeleton height={300} />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <TableSkeleton rows={10} cols={5} />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center justify-center text-center">
          <div>
            <div className="text-4xl mb-4">👥</div>
            <h3 className="text-red-800 font-medium text-lg mb-2">خطأ في تحميل البيانات</h3>
            <p className="text-red-600 text-sm mb-4">
              فشل في تحميل تحليلات سلوك المستخدمين. يرجى التحقق من الاتصال والمحاولة مرة أخرى.
            </p>
            <div className="space-x-2">
              <button
                onClick={() => refetch()}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                🔄 إعادة المحاولة
              </button>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
              >
                تحديث الصفحة
              </button>
            </div>
            {process.env.NODE_ENV === 'development' && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-sm text-red-700">
                  تفاصيل الخطأ
                </summary>
                <pre className="mt-2 p-3 bg-red-100 border border-red-300 rounded text-xs overflow-auto max-h-32">
                  {error?.toString()}
                </pre>
              </details>
            )}
          </div>
        </div>
      </div>
    );
  }

  const engagementData = prepareEngagementData();
  const regionalData = prepareRegionalData();

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              👥 تحليل سلوك المستخدمين
            </h1>
            <p className="text-gray-600 mt-1">
              تحليل شامل لسلوك المستخدمين وأنماط الاستخدام
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <select
              value={selectedDays}
              onChange={(e) => setSelectedDays(Number(e.target.value))}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value={7}>آخر 7 أيام</option>
              <option value={30}>آخر 30 يوم</option>
              <option value={90}>آخر 3 أشهر</option>
            </select>
            
            <button
              onClick={() => refetch()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
            >
              🔄 تحديث
            </button>
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      {behaviorData?.summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">إجمالي المستخدمين</h3>
                <p className="text-3xl font-bold text-blue-600 mt-2">
                  {behaviorData.summary.total_users.toLocaleString()}
                </p>
              </div>
              <div className="text-4xl">👤</div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">المستخدمون النشطون</h3>
                <p className="text-3xl font-bold text-green-600 mt-2">
                  {behaviorData.summary.active_users.toLocaleString()}
                </p>
              </div>
              <div className="text-4xl">✅</div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">مستخدمون جدد</h3>
                <p className="text-3xl font-bold text-purple-600 mt-2">
                  {behaviorData.summary.new_users.toLocaleString()}
                </p>
              </div>
              <div className="text-4xl">🆕</div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">مستخدمون عائدون</h3>
                <p className="text-3xl font-bold text-orange-600 mt-2">
                  {behaviorData.summary.returning_users.toLocaleString()}
                </p>
              </div>
              <div className="text-4xl">🔄</div>
            </div>
          </div>
        </div>
      )}

      {/* Engagement Metrics */}
      {behaviorData?.engagement_metrics && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">مقاييس المشاركة</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-gray-600">المستخدمون النشطون يومياً</p>
              <p className="text-2xl font-bold text-blue-600">
                {behaviorData.engagement_metrics.daily_active_users}
              </p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-sm text-gray-600">المستخدمون النشطون أسبوعياً</p>
              <p className="text-2xl font-bold text-green-600">
                {behaviorData.engagement_metrics.weekly_active_users}
              </p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <p className="text-sm text-gray-600">المستخدمون النشطون شهرياً</p>
              <p className="text-2xl font-bold text-purple-600">
                {behaviorData.engagement_metrics.monthly_active_users}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Engagement Scatter Plot */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">مشاركة المستخدمين</h3>
          <ResponsiveContainer width="100%" height={300}>
            <ScatterChart data={engagementData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="sessions" 
                name="الجلسات"
                label={{ value: 'عدد الجلسات', position: 'insideBottom', offset: -5 }}
              />
              <YAxis 
                dataKey="messages" 
                name="الرسائل"
                label={{ value: 'عدد الرسائل', angle: -90, position: 'insideLeft' }}
              />
              <Tooltip 
                formatter={(value, name) => [value, name === 'sessions' ? 'الجلسات' : 'الرسائل']}
                labelFormatter={(label) => `المستخدم: ${label}`}
              />
              <Scatter dataKey="messages" fill="#8884d8" />
            </ScatterChart>
          </ResponsiveContainer>
        </div>

        {/* Regional Usage */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">الاستخدام الإقليمي</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={regionalData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="users"
              >
                {regionalData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Top Users Table */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">أكثر المستخدمين نشاطاً</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المستخدم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الجلسات
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الرسائل
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  متوسط المدة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  نقاط المشاركة
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {engagementData.slice(0, 10).map((user, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {user.username}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {user.sessions}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {user.messages}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {(user.duration / 60).toFixed(1)} دقيقة
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {user.engagement.toFixed(1)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default UserBehaviorAnalytics;
