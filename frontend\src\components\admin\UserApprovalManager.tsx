/**
 * ✨ REDESIGNED User Approval Manager Component
 * Super Admin interface for managing user registrations and approvals
 * 
 * FEATURES:
 * - 📱 Fully responsive design (mobile-first)
 * - 🌍 Complete Arabic/RTL support
 * - 🎨 Consistent design system (like login page)
 * - 📊 Complete user registration data display
 * - 🔍 Advanced search and filtering
 * - ⚡ Optimized performance with pagination
 */

import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { RTLIcon, RTLText, RTLComponent } from '../../components/rtl';
import { Button, Input, Card, Badge, Loading } from '../../components/ui';
import {
  Clock,
  CheckCircle,
  XCircle,
  User,
  Calendar,
  AlertCircle,
  RefreshCw,
  ChevronDown,
  ChevronUp,
  ArrowLeft,
  ArrowRight
} from 'lucide-react';
import { authenticatedFetchWithRefresh, isAuthenticated } from '../../utils/tokenRefresh';
import { userAPI } from '../../services/api';

// ========================================
// INTERFACES
// ========================================

interface UserApproval {
  id: string;
  user: {
    id: string;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    date_joined: string;
  };
  status: 'pending' | 'approved' | 'rejected';
  reviewed_by?: {
    username: string;
    first_name: string;
    last_name: string;
  };
  reviewed_at?: string;
  rejection_reason?: string;
  admin_notes?: string;
  created_at: string;
  updated_at: string;
  user_full_name: string;
  user_email: string;
  user_username: string;
  days_pending?: number;
  requested_role_info?: {
    role_name: string;
    role_display_name: string;
    application_status: string;
    motivation: string;
    qualifications: string;
    applied_at: string;
  };
  role_specific_data?: {
    business_name?: string;
    business_stage?: string;
    funding_needed?: string;
    business_description?: string;
    industry?: string;
    team_size?: string;
    support_needed?: string;
    previous_experience?: string;
    expertise?: string;
    mentor_experience?: string;
    mentorship_areas?: string;
    availability?: string;
    preferred_communication?: string;
    investment_range?: string;
    investment_stage?: string;
    preferred_industries?: string;
    investment_criteria?: string;
    portfolio_companies?: string;
    interests?: string;
    goals?: string;
    experience?: string;
    portfolio_url?: string;
  };
  profile_summary?: {
    location?: string;
    company?: string;
    job_title?: string;
    industry?: string;
    phone_number?: string;
    bio?: string;
    language?: string;
    website?: string;
    linkedin_url?: string;
  };
}

interface ApprovalStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
}

// ========================================
// MAIN COMPONENT
// ========================================

const UserApprovalManager: React.FC = () => {
  const { language, isRTL } = useLanguage();
  
  // State management
  const [approvals, setApprovals] = useState<UserApproval[]>([]);
  const [stats, setStats] = useState<ApprovalStats>({ total: 0, pending: 0, approved: 0, rejected: 0 });
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all');
  const [roleFilter, setRoleFilter] = useState<'all' | 'entrepreneur' | 'mentor' | 'investor' | 'user'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedApproval, setSelectedApproval] = useState<UserApproval | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [pageSize] = useState(20);

  // ========================================
  // UTILITY FUNCTIONS
  // ========================================

  // TODO: Implement when UI components are added
  // const formatDisplayValue = (value: any): string => { ... };
  // const getRoleIcon = (role: string) => { ... };
  // const getStatusIcon = (status: string) => { ... };
  // const getStatusColor = (status: string) => { ... };

  // ========================================
  // API FUNCTIONS
  // ========================================

  const fetchApprovals = async (page: number = currentPage) => {
    try {
      setLoading(true);

      if (!isAuthenticated()) {
        console.warn('⚠️ User not authenticated - cannot fetch approvals');
        setApprovals([]);
        return;
      }

      const params = new URLSearchParams({
        page: page.toString(),
        page_size: pageSize.toString(),
        ...(filter !== 'all' && { status: filter }),
        ...(roleFilter !== 'all' && { role: roleFilter }),
        ...(searchTerm && { search: searchTerm })
      });

      const url = `/api/users/approvals/?${params.toString()}`;
      console.log('🔍 About to fetch approvals from:', url);
      
      const response = await authenticatedFetchWithRefresh(url);

      if (response.ok) {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const data = await response.json();
          console.log('✅ Successfully fetched approvals data:', data);
          
          if (data.results) {
            setApprovals(data.results);
            setTotalCount(data.count || 0);
            setTotalPages(Math.ceil((data.count || 0) / pageSize));
            setCurrentPage(page);
          } else {
            const approvals = Array.isArray(data) ? data : [];
            setApprovals(approvals);
            setTotalCount(approvals.length);
            setTotalPages(1);
            setCurrentPage(1);
          }
        } else {
          console.error('❌ Response is not JSON:', contentType);
          setApprovals([]);
        }
      } else {
        console.error('❌ Failed to fetch approvals:', response.status, response.statusText);
        setApprovals([]);
      }
    } catch (error) {
      console.error('❌ Error fetching approvals:', error);
      setApprovals([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      if (!isAuthenticated()) {
        console.warn('⚠️ User not authenticated - cannot fetch stats');
        return;
      }

      const data = await userAPI.getUserApprovalStats();
      console.log('📊 Approval stats:', data);
      setStats(data);
    } catch (error) {
      console.error('❌ Error fetching stats:', error);
      setStats({ total: 0, pending: 0, approved: 0, rejected: 0 });
    }
  };

  // ========================================
  // EFFECTS
  // ========================================

  useEffect(() => {
    if (isAuthenticated()) {
      fetchApprovals(1);
      fetchStats();
    } else {
      setApprovals([]);
      setStats({ total: 0, pending: 0, approved: 0, rejected: 0 });
    }
  }, [filter, roleFilter]);

  useEffect(() => {
    if (!isAuthenticated()) return;
    
    const timeoutId = setTimeout(() => {
      fetchApprovals(1);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  useEffect(() => {
    if (isAuthenticated() && currentPage > 1) {
      fetchApprovals(currentPage);
    }
  }, [currentPage]);

  // ========================================
  // ACTION FUNCTIONS
  // ========================================

  const handleApprove = async (approvalId: string) => {
    try {
      await userAPI.approveUser(approvalId);
      console.log('✅ User approved successfully');
      await fetchApprovals();
      await fetchStats();
    } catch (error) {
      console.error('❌ Error approving user:', error);
    }
  };

  const handleReject = async (approvalId: string, reason: string) => {
    try {
      await userAPI.rejectUser(approvalId, reason);
      console.log('✅ User rejected successfully');
      await fetchApprovals();
      await fetchStats();
      setShowRejectModal(false);
      setRejectionReason('');
      setSelectedApproval(null);
    } catch (error) {
      console.error('❌ Error rejecting user:', error);
    }
  };

  const openRejectModal = (approval: UserApproval) => {
    setSelectedApproval(approval);
    setShowRejectModal(true);
  };

  const closeRejectModal = () => {
    setShowRejectModal(false);
    setRejectionReason('');
    setSelectedApproval(null);
  };

  const toggleRowExpansion = (approvalId: string) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(approvalId)) {
      newExpandedRows.delete(approvalId);
    } else {
      newExpandedRows.add(approvalId);
    }
    setExpandedRows(newExpandedRows);
  };

  const fetchDetailedUserData = async (approvalId: string) => {
    try {
      const response = await authenticatedFetchWithRefresh(`/api/super_admin/approvals/${approvalId}/details/`);

      if (response.ok) {
        const data = await response.json();
        setDetailedUserData(data);
        setShowDetailModal(true);
      }
    } catch (error) {
      console.error('Error fetching detailed user data:', error);
    }
  };

  // ========================================
  // RENDER FUNCTIONS
  // ========================================

  // Show authentication message if user is not authenticated
  if (!isAuthenticated()) {
    return (
      <RTLComponent className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
        <Card variant="glass" className="p-8 text-center max-w-md w-full">
          <RTLIcon icon={AlertCircle} size={64} className="text-yellow-500 mx-auto mb-4" />
          <RTLText as="h2" className="text-2xl font-bold text-white mb-2">
            {language === 'ar' ? 'مطلوب تسجيل الدخول' : 'Authentication Required'}
          </RTLText>
          <RTLText className="text-gray-300 mb-4">
            {language === 'ar'
              ? 'يجب تسجيل الدخول كمدير لعرض موافقات المستخدمين'
              : 'You must be logged in as an admin to view user approvals'
            }
          </RTLText>
          <Button
            onClick={() => window.location.href = '/login'}
            variant="primary"
            className="w-full"
          >
            {language === 'ar' ? 'تسجيل الدخول' : 'Login'}
          </Button>
        </Card>
      </RTLComponent>
    );
  }

  return (
    <RTLComponent 
      className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-4 sm:p-6 lg:p-8"
      dir={isRTL ? 'rtl' : 'ltr'}
    >
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <Card variant="glass" className="p-6 sm:p-8">
          <div className="text-center sm:text-left">
            <RTLText 
              as="h1" 
              className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400 mb-3"
            >
              {language === 'ar' ? 'إدارة موافقات المستخدمين' : 'User Approval Management'}
            </RTLText>
            <RTLText 
              as="p" 
              className="text-gray-300 text-sm sm:text-base"
            >
              {language === 'ar'
                ? 'راجع واعتمد أو ارفض طلبات التسجيل الجديدة من المستخدمين'
                : 'Review and approve or reject new user registration requests'
              }
            </RTLText>
          </div>
        </Card>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          {/* Total Requests */}
          <Card variant="glass" className="p-4 sm:p-6 hover:bg-white/10 transition-all duration-300">
            <RTLComponent className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="flex-shrink-0 p-3 bg-blue-500/20 border border-blue-400/30 rounded-xl">
                <RTLIcon icon={Users} size={24} className="text-blue-400" />
              </div>
              <div className="flex-1 min-w-0">
                <RTLText className="text-sm font-medium text-blue-300 truncate">
                  {language === 'ar' ? 'إجمالي الطلبات' : 'Total Requests'}
                </RTLText>
                <RTLText className="text-2xl sm:text-3xl font-bold text-white">
                  {stats.total}
                </RTLText>
              </div>
            </RTLComponent>
          </Card>

          {/* Pending */}
          <Card variant="glass" className="p-4 sm:p-6 hover:bg-white/10 transition-all duration-300">
            <RTLComponent className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="flex-shrink-0 p-3 bg-yellow-500/20 border border-yellow-400/30 rounded-xl">
                <RTLIcon icon={Clock} size={24} className="text-yellow-400" />
              </div>
              <div className="flex-1 min-w-0">
                <RTLText className="text-sm font-medium text-yellow-300 truncate">
                  {language === 'ar' ? 'في الانتظار' : 'Pending'}
                </RTLText>
                <RTLText className="text-2xl sm:text-3xl font-bold text-white">
                  {stats.pending}
                </RTLText>
              </div>
            </RTLComponent>
          </Card>

          {/* Approved */}
          <Card variant="glass" className="p-4 sm:p-6 hover:bg-white/10 transition-all duration-300">
            <RTLComponent className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="flex-shrink-0 p-3 bg-green-500/20 border border-green-400/30 rounded-xl">
                <RTLIcon icon={CheckCircle} size={24} className="text-green-400" />
              </div>
              <div className="flex-1 min-w-0">
                <RTLText className="text-sm font-medium text-green-300 truncate">
                  {language === 'ar' ? 'معتمد' : 'Approved'}
                </RTLText>
                <RTLText className="text-2xl sm:text-3xl font-bold text-white">
                  {stats.approved}
                </RTLText>
              </div>
            </RTLComponent>
          </Card>

          {/* Rejected */}
          <Card variant="glass" className="p-4 sm:p-6 hover:bg-white/10 transition-all duration-300">
            <RTLComponent className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="flex-shrink-0 p-3 bg-red-500/20 border border-red-400/30 rounded-xl">
                <RTLIcon icon={XCircle} size={24} className="text-red-400" />
              </div>
              <div className="flex-1 min-w-0">
                <RTLText className="text-sm font-medium text-red-300 truncate">
                  {language === 'ar' ? 'مرفوض' : 'Rejected'}
                </RTLText>
                <RTLText className="text-2xl sm:text-3xl font-bold text-white">
                  {stats.rejected}
                </RTLText>
              </div>
            </RTLComponent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card variant="glass" className="p-4 sm:p-6">
          <div className="space-y-6">
            {/* Status Filters */}
            <div className="space-y-3">
              <RTLText className="text-sm font-medium text-gray-300">
                {language === 'ar' ? 'تصفية حسب الحالة:' : 'Filter by Status:'}
              </RTLText>
              <div className="flex flex-wrap gap-2">
                {(['all', 'pending', 'approved', 'rejected'] as const).map((filterOption) => (
                  <Button
                    key={filterOption}
                    variant={filter === filterOption ? 'primary' : 'ghost'}
                    size="sm"
                    onClick={() => setFilter(filterOption)}
                    className="text-xs sm:text-sm"
                  >
                    {language === 'ar' ? {
                      all: 'الكل',
                      pending: 'في الانتظار',
                      approved: 'معتمد',
                      rejected: 'مرفوض'
                    }[filterOption] : {
                      all: 'All',
                      pending: 'Pending',
                      approved: 'Approved',
                      rejected: 'Rejected'
                    }[filterOption]}
                  </Button>
                ))}
              </div>
            </div>

            {/* Role Filters */}
            <div className="space-y-3">
              <RTLText className="text-sm font-medium text-gray-300">
                {language === 'ar' ? 'تصفية حسب الدور:' : 'Filter by Role:'}
              </RTLText>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={roleFilter === 'all' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setRoleFilter('all')}
                  className="text-xs sm:text-sm"
                >
                  {language === 'ar' ? 'جميع الأدوار' : 'All Roles'}
                </Button>

                {(['entrepreneur', 'mentor', 'investor', 'user'] as const).map((role) => (
                  <Button
                    key={role}
                    variant={roleFilter === role ? 'primary' : 'ghost'}
                    size="sm"
                    onClick={() => setRoleFilter(role)}
                    className="text-xs sm:text-sm flex items-center gap-2"
                  >
                    {getRoleIcon(role)}
                    {language === 'ar' ? {
                      entrepreneur: 'رائد أعمال',
                      mentor: 'مرشد',
                      investor: 'مستثمر',
                      user: 'مستخدم'
                    }[role] : {
                      entrepreneur: 'Entrepreneur',
                      mentor: 'Mentor',
                      investor: 'Investor',
                      user: 'User'
                    }[role]}
                  </Button>
                ))}
              </div>
            </div>

            {/* Search and Actions */}
            <RTLComponent className="flex flex-col sm:flex-row gap-4">
              {/* Search Input */}
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder={language === 'ar' ? 'البحث بالاسم أو البريد الإلكتروني...' : 'Search by name or email...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              {/* Refresh Button */}
              <Button
                variant="ghost"
                onClick={() => { fetchApprovals(); fetchStats(); }}
                className="flex items-center gap-2 whitespace-nowrap"
              >
                <RTLIcon icon={RefreshCw} size={16} />
                {language === 'ar' ? 'تحديث' : 'Refresh'}
              </Button>
            </RTLComponent>
          </div>
        </Card>

        {/* Users Table/Cards */}
        <Card variant="glass" className="overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <Loading size="lg" />
              <RTLText className="text-gray-300 mt-4">
                {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
              </RTLText>
            </div>
          ) : approvals.length === 0 ? (
            <div className="p-8 text-center">
              <RTLIcon icon={User} size={48} className="text-gray-400 mx-auto mb-4" />
              <RTLText className="text-gray-300">
                {language === 'ar' ? 'لا توجد طلبات موافقة' : 'No approval requests found'}
              </RTLText>
            </div>
          ) : (
            <>
              {/* Mobile Cards View */}
              <div className="block lg:hidden">
                <div className="divide-y divide-white/10">
                  {approvals.map((approval) => (
                    <UserApprovalCard
                      key={approval.id}
                      approval={approval}
                      language={language}
                      isRTL={isRTL}
                      onApprove={() => handleApprove(approval.id)}
                      onReject={() => openRejectModal(approval)}
                      onViewDetails={() => fetchDetailedUserData(approval.id)}
                    />
                  ))}
                </div>
              </div>

              {/* Desktop Table View */}
              <div className="hidden lg:block overflow-x-auto">
                <UserApprovalTable
                  approvals={approvals}
                  language={language}
                  isRTL={isRTL}
                  expandedRows={expandedRows}
                  onToggleExpand={toggleRowExpansion}
                  onApprove={handleApprove}
                  onReject={openRejectModal}
                  onViewDetails={fetchDetailedUserData}
                />
              </div>
            </>
          )}
        </Card>

        {/* Pagination */}
        {!loading && totalPages > 1 && (
          <Card variant="glass" className="p-4">
            <RTLComponent className="flex items-center justify-between">
              <RTLText className="text-sm text-gray-300">
                {language === 'ar'
                  ? `عرض ${((currentPage - 1) * pageSize) + 1} إلى ${Math.min(currentPage * pageSize, totalCount)} من ${totalCount} نتيجة`
                  : `Showing ${((currentPage - 1) * pageSize) + 1} to ${Math.min(currentPage * pageSize, totalCount)} of ${totalCount} results`
                }
              </RTLText>

              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  <RTLIcon icon={ArrowLeft} size={16} flipInRTL />
                  {language === 'ar' ? 'السابق' : 'Previous'}
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? 'primary' : 'ghost'}
                        size="sm"
                        onClick={() => setCurrentPage(pageNum)}
                        className="min-w-[2.5rem]"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  {language === 'ar' ? 'التالي' : 'Next'}
                  <RTLIcon icon={ArrowRight} size={16} flipInRTL />
                </Button>
              </div>
            </RTLComponent>
          </Card>
        )}

        {/* Reject Modal */}
        {showRejectModal && selectedApproval && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <Card variant="glass" className="w-full max-w-md">
              <div className="p-6">
                <RTLText as="h3" className="text-xl font-bold text-white mb-4">
                  {language === 'ar' ? 'رفض المستخدم' : 'Reject User'}
                </RTLText>

                <RTLText className="text-gray-300 mb-4">
                  {language === 'ar'
                    ? `هل أنت متأكد من رفض ${selectedApproval.user_username}؟`
                    : `Are you sure you want to reject ${selectedApproval.user_username}?`
                  }
                </RTLText>

                <div className="mb-4">
                  <RTLText className="text-sm font-medium text-gray-300 mb-2">
                    {language === 'ar' ? 'سبب الرفض:' : 'Rejection Reason:'}
                  </RTLText>
                  <textarea
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    className="w-full p-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 resize-none"
                    rows={3}
                    placeholder={language === 'ar' ? 'اكتب سبب الرفض...' : 'Enter rejection reason...'}
                  />
                </div>

                <div className="flex gap-3 justify-end">
                  <Button
                    variant="ghost"
                    onClick={closeRejectModal}
                  >
                    {language === 'ar' ? 'إلغاء' : 'Cancel'}
                  </Button>
                  <Button
                    variant="primary"
                    onClick={() => handleReject(selectedApproval.id, rejectionReason)}
                    disabled={!rejectionReason.trim()}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {language === 'ar' ? 'رفض' : 'Reject'}
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </RTLComponent>
  );
};

// ========================================
// MOBILE CARD COMPONENT
// ========================================

interface UserApprovalCardProps {
  approval: UserApproval;
  language: string;
  isRTL: boolean;
  onApprove: () => void;
  onReject: () => void;
  onViewDetails: () => void;
}

const UserApprovalCard: React.FC<UserApprovalCardProps> = ({
  approval,
  language,
  isRTL,
  onApprove,
  onReject,
  onViewDetails
}) => {
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'entrepreneur':
        return <RTLIcon icon={DollarSign} size={16} className="text-purple-400" />;
      case 'mentor':
        return <RTLIcon icon={Award} size={16} className="text-green-400" />;
      case 'investor':
        return <RTLIcon icon={Building} size={16} className="text-yellow-400" />;
      case 'user':
        return <RTLIcon icon={User} size={16} className="text-blue-400" />;
      default:
        return <RTLIcon icon={User} size={16} className="text-gray-400" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <RTLIcon icon={Clock} size={16} className="text-yellow-500" />;
      case 'approved':
        return <RTLIcon icon={CheckCircle} size={16} className="text-green-500" />;
      case 'rejected':
        return <RTLIcon icon={XCircle} size={16} className="text-red-500" />;
      default:
        return <RTLIcon icon={AlertCircle} size={16} className="text-gray-500" />;
    }
  };

  return (
    <div className="p-4 sm:p-6 hover:bg-white/5 transition-colors">
      <div className="space-y-4">
        {/* User Info */}
        <RTLComponent className="flex items-start space-x-4 rtl:space-x-reverse">
          <div className="flex-shrink-0 w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
            <RTLIcon icon={User} size={20} className="text-gray-300" />
          </div>
          <div className="flex-1 min-w-0">
            <RTLText className="text-lg font-semibold text-white truncate">
              {approval.user_full_name}
            </RTLText>
            <RTLText className="text-sm text-gray-300 truncate">
              {approval.user.email}
            </RTLText>
            {approval.profile_summary?.location && (
              <RTLComponent className="flex items-center mt-1">
                <RTLIcon icon={MapPin} size={14} className="text-gray-400" />
                <RTLText className="text-xs text-gray-400 ml-1 rtl:mr-1">
                  {approval.profile_summary.location}
                </RTLText>
              </RTLComponent>
            )}
          </div>
        </RTLComponent>

        {/* Role and Status */}
        <RTLComponent className="flex items-center justify-between">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            {approval.requested_role_info ? (
              <Badge variant="secondary" className="flex items-center gap-2">
                {getRoleIcon(approval.requested_role_info.role_name)}
                {approval.requested_role_info.role_display_name}
              </Badge>
            ) : (
              <Badge variant="secondary">
                {language === 'ar' ? 'غير محدد' : 'Not specified'}
              </Badge>
            )}
          </div>

          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            {getStatusIcon(approval.status)}
            <RTLText className="text-sm font-medium">
              {language === 'ar' ? {
                pending: 'في الانتظار',
                approved: 'معتمد',
                rejected: 'مرفوض'
              }[approval.status] : {
                pending: 'Pending',
                approved: 'Approved',
                rejected: 'Rejected'
              }[approval.status]}
            </RTLText>
            {approval.days_pending && approval.days_pending > 0 && (
              <RTLText className="text-xs text-gray-400">
                ({approval.days_pending} {language === 'ar' ? 'يوم' : 'days'})
              </RTLText>
            )}
          </div>
        </RTLComponent>

        {/* Registration Date */}
        <RTLComponent className="flex items-center space-x-2 rtl:space-x-reverse">
          <RTLIcon icon={Calendar} size={14} className="text-gray-400" />
          <RTLText className="text-xs text-gray-400">
            {new Date(approval.created_at).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
          </RTLText>
        </RTLComponent>

        {/* Actions */}
        <RTLComponent className="flex flex-wrap gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onViewDetails}
            className="flex items-center gap-2"
          >
            <RTLIcon icon={Eye} size={14} />
            {language === 'ar' ? 'تفاصيل' : 'Details'}
          </Button>

          {approval.status === 'pending' && (
            <>
              <Button
                variant="primary"
                size="sm"
                onClick={onApprove}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
              >
                <RTLIcon icon={CheckCircle} size={14} />
                {language === 'ar' ? 'اعتماد' : 'Approve'}
              </Button>
              <Button
                variant="primary"
                size="sm"
                onClick={onReject}
                className="flex items-center gap-2 bg-red-600 hover:bg-red-700"
              >
                <RTLIcon icon={XCircle} size={14} />
                {language === 'ar' ? 'رفض' : 'Reject'}
              </Button>
            </>
          )}
        </RTLComponent>
      </div>
    </div>
  );
};

// ========================================
// DESKTOP TABLE COMPONENT
// ========================================

interface UserApprovalTableProps {
  approvals: UserApproval[];
  language: string;
  isRTL: boolean;
  expandedRows: Set<string>;
  onToggleExpand: (id: string) => void;
  onApprove: (id: string) => void;
  onReject: (approval: UserApproval) => void;
  onViewDetails: (id: string) => void;
}

const UserApprovalTable: React.FC<UserApprovalTableProps> = ({
  approvals,
  language,
  isRTL,
  expandedRows,
  onToggleExpand,
  onApprove,
  onReject,
  onViewDetails
}) => {
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'entrepreneur':
        return <RTLIcon icon={DollarSign} size={16} className="text-purple-400" />;
      case 'mentor':
        return <RTLIcon icon={Award} size={16} className="text-green-400" />;
      case 'investor':
        return <RTLIcon icon={Building} size={16} className="text-yellow-400" />;
      case 'user':
        return <RTLIcon icon={User} size={16} className="text-blue-400" />;
      default:
        return <RTLIcon icon={User} size={16} className="text-gray-400" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <RTLIcon icon={Clock} size={16} className="text-yellow-500" />;
      case 'approved':
        return <RTLIcon icon={CheckCircle} size={16} className="text-green-500" />;
      case 'rejected':
        return <RTLIcon icon={XCircle} size={16} className="text-red-500" />;
      default:
        return <RTLIcon icon={AlertCircle} size={16} className="text-gray-500" />;
    }
  };

  return (
    <table className="w-full">
      <thead className="bg-white/10 border-b border-white/20">
        <tr>
          <th className={`px-6 py-3 text-xs font-medium text-gray-300 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
            {language === 'ar' ? 'المستخدم' : 'User'}
          </th>
          <th className={`px-6 py-3 text-xs font-medium text-gray-300 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
            {language === 'ar' ? 'الدور المطلوب' : 'Requested Role'}
          </th>
          <th className={`px-6 py-3 text-xs font-medium text-gray-300 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
            {language === 'ar' ? 'الحالة' : 'Status'}
          </th>
          <th className={`px-6 py-3 text-xs font-medium text-gray-300 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
            {language === 'ar' ? 'تاريخ التسجيل' : 'Registration Date'}
          </th>
          <th className={`px-6 py-3 text-xs font-medium text-gray-300 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
            {language === 'ar' ? 'الإجراءات' : 'Actions'}
          </th>
        </tr>
      </thead>
      <tbody className="bg-transparent divide-y divide-white/20">
        {approvals.map((approval) => (
          <React.Fragment key={approval.id}>
            <tr className="hover:bg-white/10 transition-colors duration-200">
              <td className="px-6 py-4 whitespace-nowrap">
                <RTLComponent className="flex items-center">
                  <div className="flex-shrink-0 h-10 w-10">
                    <div className="h-10 w-10 rounded-full bg-white/20 flex items-center justify-center">
                      <RTLIcon icon={User} size={20} className="text-gray-300" />
                    </div>
                  </div>
                  <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                    <RTLText className="text-sm font-medium text-white">
                      {approval.user_full_name}
                    </RTLText>
                    <RTLText className="text-sm text-gray-300">
                      {approval.user.email}
                    </RTLText>
                    {approval.profile_summary?.location && (
                      <RTLComponent className="flex items-center mt-1">
                        <RTLIcon icon={MapPin} size={12} className="text-gray-400" />
                        <RTLText className="text-xs text-gray-400 ml-1 rtl:mr-1">
                          {approval.profile_summary.location}
                        </RTLText>
                      </RTLComponent>
                    )}
                  </div>
                </RTLComponent>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {approval.requested_role_info ? (
                  <RTLComponent className="flex items-center">
                    {getRoleIcon(approval.requested_role_info.role_name)}
                    <Badge variant="secondary" className={`${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {approval.requested_role_info.role_display_name}
                    </Badge>
                  </RTLComponent>
                ) : (
                  <RTLText className="text-gray-400 text-sm">
                    {language === 'ar' ? 'غير محدد' : 'Not specified'}
                  </RTLText>
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <RTLComponent className="flex items-center">
                  {getStatusIcon(approval.status)}
                  <Badge variant="secondary" className={`${isRTL ? 'mr-2' : 'ml-2'}`}>
                    {language === 'ar' ? {
                      pending: 'في الانتظار',
                      approved: 'معتمد',
                      rejected: 'مرفوض'
                    }[approval.status] : {
                      pending: 'Pending',
                      approved: 'Approved',
                      rejected: 'Rejected'
                    }[approval.status]}
                  </Badge>
                  {approval.days_pending && approval.days_pending > 0 && (
                    <RTLText className={`${isRTL ? 'mr-2' : 'ml-2'} text-xs text-gray-400`}>
                      ({approval.days_pending} {language === 'ar' ? 'يوم' : 'days'})
                    </RTLText>
                  )}
                </RTLComponent>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                {new Date(approval.created_at).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <RTLComponent className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onToggleExpand(approval.id)}
                  >
                    {expandedRows.has(approval.id) ? (
                      <RTLIcon icon={ChevronUp} size={16} />
                    ) : (
                      <RTLIcon icon={ChevronDown} size={16} />
                    )}
                    {language === 'ar' ? 'سريع' : 'Quick'}
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onViewDetails(approval.id)}
                  >
                    <RTLIcon icon={Eye} size={16} />
                    {language === 'ar' ? 'تفاصيل كاملة' : 'Full Details'}
                  </Button>

                  {approval.status === 'pending' && (
                    <>
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={() => onApprove(approval.id)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <RTLIcon icon={CheckCircle} size={16} />
                        {language === 'ar' ? 'اعتماد' : 'Approve'}
                      </Button>
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={() => onReject(approval)}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        <RTLIcon icon={XCircle} size={16} />
                        {language === 'ar' ? 'رفض' : 'Reject'}
                      </Button>
                    </>
                  )}
                </RTLComponent>
              </td>
            </tr>

            {/* Expandable Row Details */}
            {expandedRows.has(approval.id) && (
              <tr>
                <td colSpan={5} className="px-6 py-6 bg-white/5 border-t border-white/10">
                  <div className="space-y-6">
                    {/* Basic Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-3">
                        <RTLText className="text-sm font-medium text-white mb-3 flex items-center">
                          <RTLIcon icon={User} size={16} className="text-blue-400 mr-2 rtl:ml-2" />
                          {language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'}
                        </RTLText>
                        <div className="space-y-2 text-sm">
                          <RTLComponent className="flex items-center">
                            <RTLText className="text-gray-400 w-20">{language === 'ar' ? 'الاسم:' : 'Name:'}</RTLText>
                            <RTLText className="text-white">{approval.user_full_name}</RTLText>
                          </RTLComponent>
                          <RTLComponent className="flex items-center">
                            <RTLText className="text-gray-400 w-20">{language === 'ar' ? 'البريد:' : 'Email:'}</RTLText>
                            <RTLText className="text-white">{approval.user.email}</RTLText>
                          </RTLComponent>
                          <RTLComponent className="flex items-center">
                            <RTLText className="text-gray-400 w-20">{language === 'ar' ? 'المستخدم:' : 'Username:'}</RTLText>
                            <RTLText className="text-white">{approval.user.username}</RTLText>
                          </RTLComponent>
                        </div>
                      </div>

                      {/* Role Information */}
                      {approval.requested_role_info && (
                        <div className="space-y-3">
                          <RTLText className="text-sm font-medium text-white mb-3 flex items-center">
                            {getRoleIcon(approval.requested_role_info.role_name)}
                            <span className="ml-2 rtl:mr-2">{language === 'ar' ? 'معلومات الدور' : 'Role Information'}</span>
                          </RTLText>
                          <div className="space-y-2 text-sm">
                            <RTLComponent className="flex items-center">
                              <RTLText className="text-gray-400 w-20">{language === 'ar' ? 'الدور:' : 'Role:'}</RTLText>
                              <RTLText className="text-white">{approval.requested_role_info.role_display_name}</RTLText>
                            </RTLComponent>
                            {approval.requested_role_info.motivation && (
                              <div>
                                <RTLText className="text-gray-400">{language === 'ar' ? 'الدافع:' : 'Motivation:'}</RTLText>
                                <RTLText className="text-white mt-1 text-xs bg-white/10 p-2 rounded">{approval.requested_role_info.motivation}</RTLText>
                              </div>
                            )}
                            {approval.requested_role_info.qualifications && (
                              <div>
                                <RTLText className="text-gray-400">{language === 'ar' ? 'المؤهلات:' : 'Qualifications:'}</RTLText>
                                <RTLText className="text-white mt-1 text-xs bg-white/10 p-2 rounded">{approval.requested_role_info.qualifications}</RTLText>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </td>
              </tr>
            )}
          </React.Fragment>
        ))}
      </tbody>
    </table>
  );
};

export default UserApprovalManager;
