#!/usr/bin/env python
"""
Comprehensive Bug and Issue Diagnostic Tool
Finds all issues in the admin system
"""
import requests
import json
import time

def comprehensive_diagnostic():
    print('🔍 COMPREHENSIVE BUG AND ISSUE DIAGNOSTIC')
    print('=' * 60)
    
    backend_url = 'http://localhost:8000'
    frontend_url = 'http://localhost:4178'
    
    issues_found = []
    
    try:
        # Step 1: Check Server Health
        print('\n1. 🔍 Checking Server Health...')
        
        try:
            backend_response = requests.get(f'{backend_url}/api/', timeout=5)
            print(f'   Backend: {backend_response.status_code} ✅')
        except Exception as e:
            issues_found.append(f"Backend server issue: {e}")
            print(f'   ❌ Backend Error: {e}')
        
        try:
            frontend_response = requests.get(frontend_url, timeout=5)
            print(f'   Frontend: {frontend_response.status_code} ✅')
        except Exception as e:
            issues_found.append(f"Frontend server issue: {e}")
            print(f'   ❌ Frontend Error: {e}')
        
        # Step 2: Test Authentication Issues
        print('\n2. 🔐 Testing Authentication Issues...')
        
        # Test JWT endpoint
        try:
            token_response = requests.post(
                f'{backend_url}/api/auth/token/',
                json={'username': 'admin', 'password': 'admin123'},
                headers={'Content-Type': 'application/json'},
                timeout=5
            )
            
            if token_response.status_code == 200:
                token_data = token_response.json()
                access_token = token_data.get('access')
                if access_token:
                    print(f'   ✅ JWT Authentication working')
                else:
                    issues_found.append("JWT token not found in response")
                    print(f'   ❌ JWT token missing: {token_data}')
            else:
                issues_found.append(f"JWT authentication failed: {token_response.status_code}")
                print(f'   ❌ JWT Auth Failed: {token_response.status_code} - {token_response.text}')
        except Exception as e:
            issues_found.append(f"JWT authentication error: {e}")
            print(f'   ❌ JWT Error: {e}')
        
        # Step 3: Test All Admin Endpoints for Issues
        print('\n3. 📊 Testing Admin Endpoints for Issues...')
        
        if 'access_token' in locals():
            headers = {'Authorization': f'Bearer {access_token}'}
            
            # Test each endpoint
            endpoints = [
                '/api/admin/dashboard/stats/',
                '/api/admin/users/',
                '/api/admin/system/',
                '/api/admin/logs/',
                '/api/users/approvals/',
            ]
            
            for endpoint in endpoints:
                try:
                    response = requests.get(f'{backend_url}{endpoint}', headers=headers, timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        print(f'   ✅ {endpoint}: Working')
                        
                        # Check for data issues
                        if endpoint == '/api/admin/dashboard/stats/':
                            if not data.get('users'):
                                issues_found.append("Dashboard stats missing users data")
                            if not data.get('system'):
                                issues_found.append("Dashboard stats missing system data")
                        
                        elif endpoint == '/api/admin/users/':
                            if not data.get('results'):
                                issues_found.append("Users endpoint returning no results")
                            if data.get('count', 0) == 0:
                                issues_found.append("No users found in database")
                    
                    elif response.status_code == 401:
                        issues_found.append(f"Authentication issue with {endpoint}")
                        print(f'   ❌ {endpoint}: Authentication failed')
                    
                    elif response.status_code == 403:
                        issues_found.append(f"Permission denied for {endpoint}")
                        print(f'   ❌ {endpoint}: Permission denied')
                    
                    elif response.status_code == 404:
                        issues_found.append(f"Endpoint not found: {endpoint}")
                        print(f'   ❌ {endpoint}: Not found')
                    
                    elif response.status_code == 500:
                        issues_found.append(f"Server error at {endpoint}: {response.text}")
                        print(f'   ❌ {endpoint}: Server error')
                    
                    else:
                        issues_found.append(f"Unexpected status {response.status_code} for {endpoint}")
                        print(f'   ❌ {endpoint}: Status {response.status_code}')
                
                except Exception as e:
                    issues_found.append(f"Error testing {endpoint}: {e}")
                    print(f'   ❌ {endpoint}: Error - {e}')
        
        # Step 4: Check Database Issues
        print('\n4. 🗄️ Checking Database Issues...')
        
        try:
            import os
            import sys
            sys.path.insert(0, 'backend')
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
            
            import django
            django.setup()
            
            from django.contrib.auth.models import User
            from django.db import connection
            
            # Check database connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                print(f'   ✅ Database connection working')
            
            # Check user count
            user_count = User.objects.count()
            print(f'   ✅ Users in database: {user_count}')
            
            if user_count == 0:
                issues_found.append("No users in database")
            
            # Check admin users
            admin_count = User.objects.filter(is_superuser=True).count()
            print(f'   ✅ Admin users: {admin_count}')
            
            if admin_count == 0:
                issues_found.append("No admin users found")
        
        except Exception as e:
            issues_found.append(f"Database error: {e}")
            print(f'   ❌ Database Error: {e}')
        
        # Step 5: Check Frontend-Backend Communication
        print('\n5. 🌐 Checking Frontend-Backend Communication...')
        
        try:
            # Test CORS
            cors_response = requests.options(
                f'{backend_url}/api/admin/dashboard/stats/',
                headers={
                    'Origin': frontend_url,
                    'Access-Control-Request-Method': 'GET',
                    'Access-Control-Request-Headers': 'Authorization'
                },
                timeout=5
            )
            
            if cors_response.status_code in [200, 204]:
                print(f'   ✅ CORS working')
            else:
                issues_found.append(f"CORS issue: {cors_response.status_code}")
                print(f'   ❌ CORS issue: {cors_response.status_code}')
        
        except Exception as e:
            issues_found.append(f"CORS test error: {e}")
            print(f'   ❌ CORS Error: {e}')
        
        # Step 6: Check for Common Issues
        print('\n6. ⚠️ Checking for Common Issues...')
        
        # Check if servers are on correct ports
        if backend_url != 'http://localhost:8000':
            issues_found.append("Backend not on expected port 8000")
        
        # Check frontend port
        expected_frontend_ports = [3000, 4178, 5173]
        frontend_port = int(frontend_url.split(':')[-1])
        if frontend_port not in expected_frontend_ports:
            issues_found.append(f"Frontend on unexpected port {frontend_port}")
        
        # Step 7: Summary
        print('\n' + '=' * 60)
        print('🔍 DIAGNOSTIC SUMMARY')
        print('=' * 60)
        
        if issues_found:
            print(f'\n❌ ISSUES FOUND ({len(issues_found)}):')
            for i, issue in enumerate(issues_found, 1):
                print(f'   {i}. {issue}')
        else:
            print('\n✅ NO CRITICAL ISSUES FOUND')
        
        print(f'\n📊 SYSTEM STATUS:')
        print(f'   Backend: {"✅" if "Backend server issue" not in str(issues_found) else "❌"}')
        print(f'   Frontend: {"✅" if "Frontend server issue" not in str(issues_found) else "❌"}')
        print(f'   Authentication: {"✅" if "JWT authentication" not in str(issues_found) else "❌"}')
        print(f'   Database: {"✅" if "Database error" not in str(issues_found) else "❌"}')
        print(f'   APIs: {"✅" if not any("endpoint" in issue.lower() for issue in issues_found) else "❌"}')
        
        return issues_found
        
    except Exception as e:
        print(f'\n❌ DIAGNOSTIC ERROR: {e}')
        import traceback
        traceback.print_exc()
        return [f"Diagnostic error: {e}"]

if __name__ == '__main__':
    issues = comprehensive_diagnostic()
    if issues:
        print(f'\n🔧 READY TO FIX {len(issues)} ISSUES!')
    else:
        print(f'\n🎉 SYSTEM IS HEALTHY!')
