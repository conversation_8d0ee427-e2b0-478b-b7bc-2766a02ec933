"""
Admin Dashboard API Views
Provides comprehensive dashboard data for admin interface
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from django.contrib.auth.models import User
from django.db.models import Count, Q
from django.utils import timezone
from datetime import datetime, timedelta
import logging

from ..models import UserProfile, UserApproval, RoleApplication
from ..permissions import IsAdminUser
from core.services.unified_role_service import unified_role_service

logger = logging.getLogger(__name__)


class AdminDashboardStatsView(APIView):
    """
    Get comprehensive dashboard statistics for admin
    """
    permission_classes = [IsAuthenticated, IsAdminUser]

    def get(self, request):
        try:
            # Get time range from query params
            time_range = request.GET.get('range', '30d')
            
            # Calculate date range
            if time_range == '7d':
                start_date = timezone.now() - timedelta(days=7)
            elif time_range == '90d':
                start_date = timezone.now() - timedelta(days=90)
            else:  # default 30d
                start_date = timezone.now() - timedelta(days=30)

            today = timezone.now().date()

            # User Statistics
            total_users = User.objects.count()
            active_users = User.objects.filter(is_active=True).count()
            new_users_today = User.objects.filter(date_joined__date=today).count()
            new_users_period = User.objects.filter(date_joined__gte=start_date).count()

            # User Approval Statistics
            pending_approvals = UserApproval.objects.filter(status='pending').count()
            approved_today = UserApproval.objects.filter(
                status='approved',
                updated_at__date=today
            ).count()
            rejected_today = UserApproval.objects.filter(
                status='rejected',
                updated_at__date=today
            ).count()

            # Role Application Statistics
            pending_applications = RoleApplication.objects.filter(status='pending').count()
            total_applications = RoleApplication.objects.count()

            # User Growth Calculation
            previous_period_start = start_date - (timezone.now() - start_date)
            previous_period_users = User.objects.filter(
                date_joined__gte=previous_period_start,
                date_joined__lt=start_date
            ).count()
            
            growth_rate = 0
            if previous_period_users > 0:
                growth_rate = ((new_users_period - previous_period_users) / previous_period_users) * 100

            # User Types Distribution
            user_types = []
            
            # Count users by role applications
            entrepreneur_count = RoleApplication.objects.filter(
                requested_role__name='entrepreneur',
                status='approved'
            ).count()
            
            mentor_count = RoleApplication.objects.filter(
                requested_role__name='mentor',
                status='approved'
            ).count()
            
            investor_count = RoleApplication.objects.filter(
                requested_role__name='investor',
                status='approved'
            ).count()
            
            # Regular users (no special role)
            regular_users = total_users - entrepreneur_count - mentor_count - investor_count
            
            user_types = [
                {'name': 'Regular Users', 'value': regular_users, 'color': '#6B7280'},
                {'name': 'Entrepreneurs', 'value': entrepreneur_count, 'color': '#3B82F6'},
                {'name': 'Mentors', 'value': mentor_count, 'color': '#10B981'},
                {'name': 'Investors', 'value': investor_count, 'color': '#F59E0B'}
            ]

            # Recent Activity (last 7 days)
            last_7_days = []
            for i in range(7):
                date = timezone.now().date() - timedelta(days=6-i)
                registrations = User.objects.filter(date_joined__date=date).count()
                approvals = UserApproval.objects.filter(
                    updated_at__date=date,
                    status='approved'
                ).count()
                rejections = UserApproval.objects.filter(
                    updated_at__date=date,
                    status='rejected'
                ).count()
                
                last_7_days.append({
                    'date': date.strftime('%m/%d'),
                    'registrations': registrations,
                    'approvals': approvals,
                    'rejections': rejections
                })

            # Events Statistics (import Event model)
            try:
                from api.models import Event
                total_events = Event.objects.count()
                upcoming_events = Event.objects.filter(date__gte=timezone.now()).count()
                new_events_today = Event.objects.filter(created_at__date=today).count()
            except ImportError:
                total_events = 0
                upcoming_events = 0
                new_events_today = 0

            # Resources Statistics (import Resource model)
            try:
                from api.models import Resource
                total_resources = Resource.objects.count()
                new_resources_today = Resource.objects.filter(created_at__date=today).count()

                # Resources by type
                resources_by_type = {}
                resource_types = Resource.objects.values('resource_type').annotate(count=Count('id'))
                for item in resource_types:
                    resources_by_type[item['resource_type']] = item['count']
            except ImportError:
                total_resources = 0
                new_resources_today = 0
                resources_by_type = {}

            # Posts Statistics (import Post model)
            try:
                from api.models import Post
                total_posts = Post.objects.count()
                new_posts_today = Post.objects.filter(created_at__date=today).count()

                # Popular posts (top 5 by likes or comments)
                popular_posts = []
                top_posts = Post.objects.annotate(
                    like_count=Count('id')  # Simplified - adjust based on your like model
                ).order_by('-like_count')[:5]

                for post in top_posts:
                    popular_posts.append({
                        'id': post.id,
                        'title': post.title,
                        'like_count': post.like_count if hasattr(post, 'like_count') else 0,
                        'author': post.author.username if post.author else 'Unknown'
                    })
            except ImportError:
                total_posts = 0
                new_posts_today = 0
                popular_posts = []

            # System Health (mock data for now)
            system_health = {
                'uptime': 99.9,
                'response_time': 120,
                'error_rate': 0.1,
                'active_connections': 45
            }

            dashboard_data = {
                'users': {
                    'total': total_users,
                    'active': active_users,
                    'pending': pending_approvals,
                    'new_today': new_users_today,
                    'growth_rate': round(growth_rate, 1),
                    # Frontend compatibility
                    'total_users': total_users,
                    'active_users': active_users,
                    'new_users': new_users_today
                },
                'events': {
                    'total_events': total_events,
                    'upcoming_events': upcoming_events,
                    'new_events': new_events_today
                },
                'resources': {
                    'total_resources': total_resources,
                    'resources_by_type': resources_by_type,
                    'new_resources': new_resources_today
                },
                'posts': {
                    'total_posts': total_posts,
                    'popular_posts': popular_posts,
                    'new_posts': new_posts_today
                },
                'approvals': {
                    'pending': pending_approvals,
                    'approved_today': approved_today,
                    'rejected_today': rejected_today,
                    'total_applications': total_applications
                },
                'system': system_health,
                'activity': {
                    'last_7_days': last_7_days,
                    'user_types': user_types
                },
                'time_range': time_range,
                'generated_at': timezone.now().isoformat()
            }

            return Response(dashboard_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error generating dashboard stats: {e}")
            return Response(
                {'error': 'Failed to generate dashboard statistics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AdminSystemHealthView(APIView):
    """
    Get system health metrics
    """
    permission_classes = [IsAuthenticated, IsAdminUser]

    def get(self, request):
        try:
            # Mock system health data
            # In a real implementation, this would check actual system metrics
            health_data = {
                'uptime': 99.9,
                'response_time': 120,
                'error_rate': 0.1,
                'active_connections': 45,
                'memory_usage': 65.2,
                'cpu_usage': 23.8,
                'disk_usage': 45.1,
                'database_connections': 12,
                'last_backup': (timezone.now() - timedelta(hours=6)).isoformat(),
                'status': 'healthy'
            }

            return Response(health_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return Response(
                {'error': 'Failed to get system health'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AdminActivityLogsView(APIView):
    """
    Get recent admin activity logs
    """
    permission_classes = [IsAuthenticated, IsAdminUser]

    def get(self, request):
        try:
            limit = int(request.GET.get('limit', 10))
            
            # Get recent user approvals and applications
            recent_approvals = UserApproval.objects.select_related('user').order_by('-updated_at')[:limit//2]
            recent_applications = RoleApplication.objects.select_related('user').order_by('-created_at')[:limit//2]
            
            activities = []
            
            # Add approval activities
            for approval in recent_approvals:
                activities.append({
                    'id': f"approval_{approval.id}",
                    'type': 'approval' if approval.status == 'approved' else 'rejection',
                    'user': f"{approval.user.first_name} {approval.user.last_name}".strip() or approval.user.username,
                    'action': f"User {approval.status}",
                    'timestamp': approval.updated_at.isoformat(),
                    'status': 'success' if approval.status == 'approved' else 'warning'
                })
            
            # Add application activities
            for application in recent_applications:
                activities.append({
                    'id': f"application_{application.id}",
                    'type': 'registration',
                    'user': f"{application.user.first_name} {application.user.last_name}".strip() or application.user.username,
                    'action': f"Applied for {application.requested_role.display_name}",
                    'timestamp': application.created_at.isoformat(),
                    'status': 'success'
                })
            
            # Sort by timestamp (most recent first)
            activities.sort(key=lambda x: x['timestamp'], reverse=True)
            
            return Response(activities[:limit], status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error getting activity logs: {e}")
            return Response(
                {'error': 'Failed to get activity logs'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AdminUsersView(APIView):
    """
    Admin Users Management API
    Provides CRUD operations for user management
    """
    permission_classes = [IsAuthenticated, IsAdminUser]

    def get(self, request):
        """Get all users with pagination and filtering"""
        try:
            # Get query parameters
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 20))
            search = request.GET.get('search', '')
            status_filter = request.GET.get('status', '')
            role_filter = request.GET.get('role', '')

            # Base queryset - use prefetch_related for optional profile relationship
            users = User.objects.all().prefetch_related('profile').order_by('-date_joined')

            # Apply search filter
            if search:
                users = users.filter(
                    Q(username__icontains=search) |
                    Q(email__icontains=search) |
                    Q(first_name__icontains=search) |
                    Q(last_name__icontains=search)
                )

            # Apply status filter
            if status_filter:
                if status_filter == 'active':
                    users = users.filter(is_active=True)
                elif status_filter == 'inactive':
                    users = users.filter(is_active=False)

            # Apply role filter (skip for now since roles are handled by unified service)
            # TODO: Implement role filtering using unified_role_service
            # if role_filter:
            #     # Role filtering requires checking unified_role_service for each user
            #     pass

            # Pagination
            total_count = users.count()
            start = (page - 1) * page_size
            end = start + page_size
            users_page = users[start:end]

            # Serialize user data
            users_data = []
            for user in users_page:
                user_data = {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'is_active': user.is_active,
                    'is_staff': user.is_staff,
                    'is_superuser': user.is_superuser,
                    'date_joined': user.date_joined.isoformat(),
                    'last_login': user.last_login.isoformat() if user.last_login else None,
                }

                # Add profile data and role information
                try:
                    # Get user role from unified role service
                    user_role = unified_role_service.get_user_primary_role(user)

                    # Get profile data if available (correct relationship name is 'profile')
                    profile = getattr(user, 'profile', None)
                    if profile:
                        user_data.update({
                            'user_role': user_role,
                            'phone_number': getattr(profile, 'phone_number', ''),
                            'location': getattr(profile, 'location', ''),
                            'company': getattr(profile, 'company', ''),
                            'job_title': getattr(profile, 'job_title', ''),
                            'bio': getattr(profile, 'bio', ''),
                        })
                    else:
                        # Set default values if no profile exists
                        user_data.update({
                            'user_role': user_role,
                            'phone_number': '',
                            'location': '',
                            'company': '',
                            'job_title': '',
                            'bio': '',
                        })
                except Exception as e:
                    logger.warning(f"Error accessing user profile for {user.username}: {e}")
                    # Set default values on error
                    user_data.update({
                        'user_role': 'user',  # Default role
                        'phone_number': '',
                        'location': '',
                        'company': '',
                        'job_title': '',
                        'bio': '',
                    })

                users_data.append(user_data)

            return Response({
                'count': total_count,
                'next': f'?page={page + 1}' if end < total_count else None,
                'previous': f'?page={page - 1}' if page > 1 else None,
                'results': users_data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error getting users: {e}")
            return Response(
                {'error': 'Failed to get users'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
